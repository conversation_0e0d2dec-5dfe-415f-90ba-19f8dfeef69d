import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import "./globals.css";

const metroSans = localFont({
  src: [
    // Light weights
    {
      path: '../../../public/fonts/metro-sans/MetroSans-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-LightItalic.ttf',
      weight: '300',
      style: 'italic',
    },
    // Book weights (normal)
    {
      path: '../../../public/fonts/metro-sans/MetroSans-Book.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-BookItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Regular weights (also normal, but different variant)
    {
      path: '../../../public/fonts/metro-sans/MetroSans-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-RegularItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    // Medium weights
    {
      path: '../../../public/fonts/metro-sans/MetroSans-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-MediumItalic.ttf',
      weight: '500',
      style: 'italic',
    },
    // SemiBold weights
    {
      path: '../../../public/fonts/metro-sans/MetroSans-SemiBold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-SemiBoldItalic.ttf',
      weight: '600',
      style: 'italic',
    },
    // Bold weights
    {
      path: '../../../public/fonts/metro-sans/MetroSans-Bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../../public/fonts/metro-sans/MetroSans-BoldItalic.ttf',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-metro-sans',
  display: 'swap',
});

export const metadata: Metadata = {
  title: "AlbatrosDoc",
  description: "Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe",
};

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body
        className={`${metroSans.variable} antialiased font-metro-sans`}
      >
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
