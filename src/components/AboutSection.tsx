'use client';

import { useTranslations } from 'next-intl';

const AboutSection = () => {
  const t = useTranslations('hero');
  const features = useTranslations('features');

  return (
    <section className="min-h-screen flex">
      {/* Left Half - Image */}
      <div className="w-1/2 relative overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/building.png)'
          }}
        ></div>
        {/* Optional overlay for better text contrast if needed */}
        <div className="absolute inset-0 bg-black/10"></div>
      </div>

      {/* Right Half - Content */}
      <div className="w-1/2 bg-albatros-ivory flex items-center justify-center px-12 lg:px-16">
        <div className="max-w-2xl space-y-12">
          {/* Main Title */}
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-albatros-black leading-tight">
              Uz Albatros dokumenti klijenata stižu brzo i sigurno, bez čekanja i nepotrebnih briga.
            </h2>

            {/* Divider Line */}
            <div className="w-24 h-1 bg-albatros-carmine"></div>
          </div>

          {/* Features List */}
          <div className="space-y-8">
            <div className="flex items-center space-x-4 text-albatros-black">
              <div className="w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0"></div>
              <span className="text-xl lg:text-2xl font-medium">{features('fast')}</span>
            </div>

            <div className="flex items-center space-x-4 text-albatros-black">
              <div className="w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0"></div>
              <span className="text-xl lg:text-2xl font-medium">{features('reliable')}</span>
            </div>

            <div className="flex items-center space-x-4 text-albatros-black">
              <div className="w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0"></div>
              <span className="text-xl lg:text-2xl font-medium">{features('discrete')}</span>
            </div>
          </div>

          {/* Bottom Text */}
          <div className="pt-8 border-t border-albatros-black/20">
            <p className="text-lg lg:text-xl text-albatros-black/80 leading-relaxed">
              {t('cta')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
