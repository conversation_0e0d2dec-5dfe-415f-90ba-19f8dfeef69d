'use client';

import { useEffect } from 'react';
import gradientGL from 'gradient-gl';

const FeaturesSection = () => {
  useEffect(() => {
    // Initialize gradient-gl for features section
    gradientGL('b5.fc04', '#features-gradient-bg');
  }, []);

  return (
    <section className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div id="features-gradient-bg" className="gradient-container absolute inset-0 z-0"></div>
      {/* <div className="grain !opacity-20"></div> */}


      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        <div className="flex items-center min-h-screen">
          {/* Left side - Glass morphism container */}
          <div className="w-full md:w-1/2 lg:w-1/2">
            <div className="bg-white/10 backdrop-blur-[15px] rounded-3xl p-12">
              {/* Main Title */}
              <h2 className="text-4xl lg:text-5xl font-bold text-albatros-ivory mb-8 leading-tight">
                Na<PERSON>e usluge
              </h2>

              {/* Description */}
              <p className="text-lg text-albatros-ivory/90 mb-8 leading-relaxed">
                Nudimo širok spektar administrativnih usluga:
              </p>

              {/* Services List */}
              <div className="space-y-4 mb-8">
                <div className="flex items-center text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Pribavljanje i dostava ličnih dokumenata</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Pribavljanje i dostava izvoda iz matičnih knjiga</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Pribavljanje i dostava potvrda i uvjerenja</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Ovjera kod nadležnih institucija (općina, notar, sud)</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Pribavljanje i dostava zemljišnoknjižnih izvadaka</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Administrativna podrška pravnim licima</span>
                </div>
                <div className="flex items-start text-albatros-ivory">
                  <span className="text-albatros-ivory/60 block mr-3 h-[17px] flex items-center">•</span>
                  <span className="text-sm">Organizacija prijevoda kod sudskog tumača</span>
                </div>
              </div>

              <p className="text-sm text-albatros-ivory/80 mb-8 italic">
                Djelatnosti OD Albatros proširuju se shodno Vašim administrativnim potrebama.
              </p>

              {/* Important Notes */}
              <div className="mt-8 pt-6 border-t border-white/20">
                <h3 className="text-lg font-semibold text-albatros-ivory mb-4">Napomena:</h3>
                <div className="space-y-3 text-sm text-albatros-ivory/80">
                  <p>• Vremenski rok pribavljanja dokumenata zavisi od vrste dokumenata i vremena potrebnog institucijama.</p>
                  <p>• Za obavljanje naših djelatnosti potrebna nam je Vaša ovjerena punomoć.</p>
                  <p>• Građani BiH u BiH punomoć vade i ovjeravaju u općini.</p>
                  <p>• Državljani Bosne i Hercegovine u inostranstvu punomoć mogu ovjeriti u konzulatu.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
