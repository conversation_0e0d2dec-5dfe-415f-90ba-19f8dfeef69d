'use client';

import { useTranslations } from 'next-intl';
import Button from '@/components/ui/Button';

const FeaturesSection = () => {
  const t = useTranslations('features');

  const features = [
    {
      key: 'fast',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      gradient: 'from-yellow-400 to-orange-500'
    },
    {
      key: 'reliable',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      gradient: 'from-green-400 to-emerald-500'
    },
    {
      key: 'discrete',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
      gradient: 'from-blue-400 to-indigo-500'
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Geometric elements */}
        <div className="absolute top-20 left-20 w-72 h-72 border border-white/10 rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 border border-blue-400/20 rounded-full"></div>

        {/* Floating dots */}
        <div className="absolute top-1/4 right-1/4 w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/3 left-1/3 w-2 h-2 bg-white/60 rounded-full animate-pulse delay-1000"></div>

        {/* Gradient overlays */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-indigo-500/20 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-semibold mb-6">
            Naše prednosti
          </div>
          <h2 className="text-5xl md:text-6xl font-black leading-tight text-white mb-6">
            Tehnologija i
            <span className="block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
              inovacija uticaj
            </span>
          </h2>
          <p className="text-xl text-blue-100/80 max-w-2xl mx-auto leading-relaxed">
            Tri ključna elementa koji nas čine vašim idealnim partnerom za budućnost
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={feature.key}
              className={`group relative bg-white/10 backdrop-blur-xl rounded-3xl p-10 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-4 border border-white/20 ${
                index === 1 ? 'md:mt-12' : ''
              }`}
            >
              {/* Icon */}
              <div className={`relative w-20 h-20 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center text-white mb-8 group-hover:scale-110 transition-transform duration-300 shadow-2xl shadow-blue-500/25`}>
                {feature.icon}
              </div>

              {/* Content */}
              <div className="relative">
                <h3 className="text-2xl font-bold text-white mb-6 group-hover:text-blue-300 transition-colors duration-300">
                  {t(feature.key as 'fast' | 'reliable' | 'discrete')}
                </h3>

                {/* Description based on feature */}
                <p className="text-blue-100/80 leading-relaxed text-lg">
                  {feature.key === 'fast' && 'Brza obrada i dostava dokumenata u najkraćem mogućem roku uz najnoviju tehnologiju.'}
                  {feature.key === 'reliable' && 'Pouzdanost i profesionalnost u svakom koraku procesa sa 99.9% uspešnosti.'}
                  {feature.key === 'discrete' && 'Potpuna diskrecija i sigurnost vaših ličnih podataka sa end-to-end enkripcijom.'}
                </p>
              </div>

              {/* Decorative Element */}
              <div className={`absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r ${feature.gradient} rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>

              {/* Glow effect */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <p className="text-blue-200 text-lg mb-8">
            Spremni da iskusite razliku?
          </p>
          <Button variant="primary" size="md">
            Započnite danas
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
