'use client';

import { useTranslations } from 'next-intl';
import Button from '@/components/ui/Button';

const CTASection = () => {
  const t = useTranslations('hero');

  return (
    <section className="py-32 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Sophisticated Background Elements */}
      <div className="absolute inset-0">
        {/* Large geometric shapes */}
        <div className="absolute top-20 right-20 w-80 h-80 border border-gray-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-20 left-20 w-64 h-64 border border-blue-200 rounded-full opacity-40"></div>

        {/* Gradient overlays */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-100/50 to-transparent rounded-full blur-3xl"></div>

        {/* Connecting lines */}
        <svg className="absolute inset-0 w-full h-full opacity-20" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="ctaLineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />
              <stop offset="100%" stopColor="rgba(156, 163, 175, 0.2)" />
            </linearGradient>
          </defs>
          <path d="M 200 100 Q 500 200 800 150" stroke="url(#ctaLineGradient)" strokeWidth="1" fill="none" />
          <path d="M 100 400 Q 400 300 700 450" stroke="url(#ctaLineGradient)" strokeWidth="1" fill="none" />
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="text-center space-y-16">
          {/* Main Content */}
          <div className="space-y-8">
            <div className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              Spremni za početak?
            </div>
            <h2 className="text-5xl md:text-6xl font-black leading-tight text-gray-900">
              Bolt Lifetime data
              <span className="block bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">
                tracking
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t('cta')} Pridružite se hiljadama zadovoljnih klijenata koji su već iskusili našu revolucionarnu uslugu.
            </p>
          </div>

          {/* Enhanced CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Button variant="primary" size="lg">
              {t('contactButton')}
            </Button>

            <Button variant="secondary" size="lg" className="flex items-center space-x-3">
              <span>{t('learnMore')}</span>
              <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Button>
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="pt-16">
            <p className="text-gray-500 mb-12 text-lg">Zašto nas biraju klijenti širom sveta</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-bold text-gray-900 text-xl">100% Pouzdano</p>
                    <p className="text-gray-600">Garantovana usluga sa 99.9% uspešnosti</p>
                  </div>
                </div>
              </div>

              <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-bold text-gray-900 text-xl">24h Dostava</p>
                    <p className="text-gray-600">Brza obrada sa AI tehnologijom</p>
                  </div>
                </div>
              </div>

              <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-bold text-gray-900 text-xl">Potpuna Diskrecija</p>
                    <p className="text-gray-600">End-to-end enkripcija podataka</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="pt-16 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-black text-blue-600 mb-2">10K+</div>
                <div className="text-gray-600">Zadovoljnih klijenata</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-black text-green-600 mb-2">99.9%</div>
                <div className="text-gray-600">Uspešnost</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-black text-purple-600 mb-2">24h</div>
                <div className="text-gray-600">Prosečno vreme</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-black text-cyan-600 mb-2">5★</div>
                <div className="text-gray-600">Prosečna ocena</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
