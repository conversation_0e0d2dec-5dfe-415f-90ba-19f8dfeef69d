import React from 'react';
import { cn } from '@/lib/utils';

export interface NavButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'language' | 'mobile' | 'dropdown-item';
  isScrolled?: boolean;
  isActive?: boolean;
  children: React.ReactNode;
  className?: string;
}

const NavButton = React.forwardRef<HTMLButtonElement, NavButtonProps>(
  ({ 
    variant = 'language', 
    isScrolled = false,
    isActive = false,
    children, 
    className, 
    ...props 
  }, ref) => {
    const baseStyles = `
      transition-all duration-300 focus:outline-none
    `;

    const variants = {
      language: `
        flex items-center space-x-3 px-4 py-2 text-sm font-medium rounded-xl border
        ${isScrolled 
          ? 'text-white/80 hover:text-white hover:bg-white/10 border-white/20' 
          : 'text-white/90 hover:text-white hover:bg-white/10 border-white/30 backdrop-blur-sm drop-shadow-lg'
        }
      `,
      mobile: `
        p-2 rounded-lg
        ${isScrolled
          ? 'text-white/80 hover:text-white hover:bg-white/10'
          : 'text-white/90 hover:text-white hover:bg-white/10 backdrop-blur-sm drop-shadow-lg'
        }
      `,
      'dropdown-item': `
        flex items-center space-x-3 w-full px-4 py-3 text-sm 
        hover:bg-white/10 transition-colors rounded-lg mx-2
        ${isActive 
          ? 'bg-blue-500/20 text-blue-300' 
          : 'text-white/80 hover:text-white'
        }
      `
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

NavButton.displayName = 'NavButton';

export default NavButton;
