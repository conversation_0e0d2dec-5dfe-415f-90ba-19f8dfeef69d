import React from 'react';
import { useTranslations } from 'next-intl';
import Button from '@/components/ui/Button';
import NavButton from '@/components/ui/NavButton';

/**
 * Example component demonstrating the usage of standardized Button and NavButton components
 * This file serves as documentation and can be used for testing the button components
 */
const ButtonExamples = () => {
  const t = useTranslations('hero');

  return (
    <div className="p-8 space-y-12 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">AlbatrosDoc Button Components</h1>
        
        {/* Content Buttons */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">Content Buttons</h2>
          <p className="text-gray-600">Use these for main content areas like hero sections, CTAs, and forms.</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Primary Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-700">Primary Buttons</h3>
              <div className="space-y-3">
                <Button variant="primary" size="sm">Small Primary</Button>
                <Button variant="primary" size="md">Medium Primary</Button>
                <Button variant="primary" size="lg">Large Primary</Button>
                <Button variant="primary" size="md" isLoading>Loading...</Button>
              </div>
            </div>

            {/* Secondary Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-700">Secondary Buttons</h3>
              <div className="space-y-3">
                <Button variant="secondary" size="sm">Small Secondary</Button>
                <Button variant="secondary" size="md">Medium Secondary</Button>
                <Button variant="secondary" size="lg">Large Secondary</Button>
                <Button variant="secondary" size="md" disabled>Disabled</Button>
              </div>
            </div>

            {/* Accent Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-700">Accent Buttons (Carmine)</h3>
              <div className="space-y-3">
                <Button variant="accent" size="sm">Small Accent</Button>
                <Button variant="accent" size="md">Medium Accent</Button>
                <Button variant="accent" size="lg">Large Accent</Button>
              </div>
            </div>

            {/* Ghost Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-700">Ghost Buttons</h3>
              <div className="space-y-3">
                <Button variant="ghost" size="sm">Small Ghost</Button>
                <Button variant="ghost" size="md">Medium Ghost</Button>
                <Button variant="ghost" size="lg">Large Ghost</Button>
              </div>
            </div>

            {/* Outline Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-700">Outline Buttons</h3>
              <div className="space-y-3">
                <Button variant="outline" size="sm">Small Outline</Button>
                <Button variant="outline" size="md">Medium Outline</Button>
                <Button variant="outline" size="lg">Large Outline</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Navigation Buttons */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">Navigation Buttons</h2>
          <p className="text-gray-600">Use these for navigation elements like language switchers and mobile menus.</p>
          
          <div className="bg-gray-800 p-6 rounded-lg space-y-6">
            {/* Language Button */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white">Language Switcher</h3>
              <div className="flex gap-4">
                <NavButton variant="language" isScrolled={false}>
                  <span className="text-lg">🇺🇸</span>
                  <span className="hidden sm:block font-medium">English</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </NavButton>
                
                <NavButton variant="language" isScrolled={true}>
                  <span className="text-lg">🇺🇸</span>
                  <span className="hidden sm:block font-medium">English (Scrolled)</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </NavButton>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white">Mobile Menu Button</h3>
              <div className="flex gap-4">
                <NavButton variant="mobile" isScrolled={false}>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </NavButton>
                
                <NavButton variant="mobile" isScrolled={true}>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </NavButton>
              </div>
            </div>

            {/* Dropdown Items */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-white">Dropdown Items</h3>
              <div className="bg-slate-800/95 backdrop-blur-xl rounded-xl shadow-2xl py-2 border border-white/10 max-w-48">
                <NavButton variant="dropdown-item" isActive={true}>
                  <span>🇺🇸</span>
                  <span>English</span>
                </NavButton>
                <NavButton variant="dropdown-item" isActive={false}>
                  <span>🇩🇪</span>
                  <span>Deutsch</span>
                </NavButton>
                <NavButton variant="dropdown-item" isActive={false}>
                  <span>🇧🇦</span>
                  <span>Bosanski</span>
                </NavButton>
              </div>
            </div>
          </div>
        </section>

        {/* Usage Examples */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-gray-800">Usage Examples</h2>
          
          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-medium text-gray-700">Hero Section Style</h3>
            <div className="flex flex-col sm:flex-row gap-6">
              <Button variant="primary" size="md">
                {t('contactButton')}
              </Button>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-medium text-gray-700">CTA Section Style</h3>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button variant="primary" size="lg">
                {t('contactButton')}
              </Button>
              <Button variant="secondary" size="lg" className="flex items-center space-x-3">
                <span>{t('learnMore')}</span>
                <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Button>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border space-y-4">
            <h3 className="text-lg font-medium text-gray-700">Translated Buttons Demo</h3>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">These buttons automatically translate based on the current locale:</p>
              <div className="flex flex-wrap gap-3">
                <Button variant="primary" size="sm">{t('contactButton')}</Button>
                <Button variant="secondary" size="sm">{t('learnMore')}</Button>
                <Button variant="accent" size="sm">{t('contactButton')}</Button>
                <Button variant="ghost" size="sm">{t('contactButton')}</Button>
                <Button variant="outline" size="sm">{t('learnMore')}</Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ButtonExamples;
