/* Metro Sans Font Family - Official Stylesheet */

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-Light.eot');
    src: local('Metro Sans Light'), local('MetroSans-Light'),
        url('MetroSans-Light.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-Light.woff2') format('woff2'),
        url('MetroSans-Light.woff') format('woff'),
        url('MetroSans-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-LightItalic.eot');
    src: local('Metro Sans Light Italic'), local('MetroSans-LightItalic'),
        url('MetroSans-LightItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-LightItalic.woff2') format('woff2'),
        url('MetroSans-LightItalic.woff') format('woff'),
        url('MetroSans-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-Book.eot');
    src: local('Metro Sans Book'), local('MetroSans-Book'),
        url('MetroSans-Book.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-Book.woff2') format('woff2'),
        url('MetroSans-Book.woff') format('woff'),
        url('MetroSans-Book.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-BookItalic.eot');
    src: local('Metro Sans Book Italic'), local('MetroSans-BookItalic'),
        url('MetroSans-BookItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-BookItalic.woff2') format('woff2'),
        url('MetroSans-BookItalic.woff') format('woff'),
        url('MetroSans-BookItalic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-Regular.eot');
    src: local('Metro Sans Regular'), local('MetroSans-Regular'),
        url('MetroSans-Regular.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-Regular.woff2') format('woff2'),
        url('MetroSans-Regular.woff') format('woff'),
        url('MetroSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-RegularItalic.eot');
    src: local('Metro Sans Regular Italic'), local('MetroSans-RegularItalic'),
        url('MetroSans-RegularItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-RegularItalic.woff2') format('woff2'),
        url('MetroSans-RegularItalic.woff') format('woff'),
        url('MetroSans-RegularItalic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-Medium.eot');
    src: local('Metro Sans Medium'), local('MetroSans-Medium'),
        url('MetroSans-Medium.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-Medium.woff2') format('woff2'),
        url('MetroSans-Medium.woff') format('woff'),
        url('MetroSans-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-MediumItalic.eot');
    src: local('Metro Sans Medium Italic'), local('MetroSans-MediumItalic'),
        url('MetroSans-MediumItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-MediumItalic.woff2') format('woff2'),
        url('MetroSans-MediumItalic.woff') format('woff'),
        url('MetroSans-MediumItalic.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-SemiBold.eot');
    src: local('Metro Sans Semi Bold'), local('MetroSans-SemiBold'),
        url('MetroSans-SemiBold.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-SemiBold.woff2') format('woff2'),
        url('MetroSans-SemiBold.woff') format('woff'),
        url('MetroSans-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-SemiBoldItalic.eot');
    src: local('Metro Sans Semi Bold Italic'), local('MetroSans-SemiBoldItalic'),
        url('MetroSans-SemiBoldItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-SemiBoldItalic.woff2') format('woff2'),
        url('MetroSans-SemiBoldItalic.woff') format('woff'),
        url('MetroSans-SemiBoldItalic.ttf') format('truetype');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-Bold.eot');
    src: local('Metro Sans Bold'), local('MetroSans-Bold'),
        url('MetroSans-Bold.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-Bold.woff2') format('woff2'),
        url('MetroSans-Bold.woff') format('woff'),
        url('MetroSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Metro Sans';
    src: url('MetroSans-BoldItalic.eot');
    src: local('Metro Sans Bold Italic'), local('MetroSans-BoldItalic'),
        url('MetroSans-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('MetroSans-BoldItalic.woff2') format('woff2'),
        url('MetroSans-BoldItalic.woff') format('woff'),
        url('MetroSans-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}
