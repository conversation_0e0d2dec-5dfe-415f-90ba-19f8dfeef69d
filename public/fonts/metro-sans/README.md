# Metro Sans Font Setup

This directory contains the Metro Sans font files for the AlbatrosDoc website.

## Font Files Structure

Place your Metro Sans font files in this directory. The font package includes these files:

### Font Files (all formats included)
**Light (300 weight):**
- `MetroSans-Light.eot/woff/woff2/ttf`
- `MetroSans-LightItalic.eot/woff/woff2/ttf`

**Book/Regular (400 weight):**
- `MetroSans-Book.eot/woff/woff2/ttf`
- `MetroSans-BookItalic.eot/woff/woff2/ttf`
- `MetroSans-Regular.eot/woff/woff2/ttf`
- `MetroSans-RegularItalic.eot/woff/woff2/ttf`

**Medium (500 weight):**
- `MetroSans-Medium.eot/woff/woff2/ttf`
- `MetroSans-MediumItalic.eot/woff/woff2/ttf`

**SemiBold (600 weight):**
- `MetroSans-SemiBold.eot/woff/woff2/ttf`
- `MetroSans-SemiBoldItalic.eot/woff/woff2/ttf`

**Bold (700 weight):**
- `MetroSans-Bold.eot/woff/woff2/ttf`
- `MetroSans-BoldItalic.eot/woff/woff2/ttf`

## Usage

The font is configured as a local font in Next.js and is available throughout the application via:

- CSS variable: `var(--font-metro-sans)`
- Tailwind class: `font-metro-sans`
- CSS font-family: `'Metro Sans'`

## Configuration Files

The font is configured in:
- `/src/app/[locale]/layout.tsx` - Next.js local font configuration
- `/src/app/[locale]/globals.css` - CSS variables and Tailwind theme
- `/public/fonts/metro-sans/metro-sans.css` - Font face declarations (backup)

## Notes

- The font uses `font-display: swap` for optimal loading performance
- Fallback fonts are Arial, Helvetica, sans-serif
- The font is optimized for web use with antialiasing enabled
