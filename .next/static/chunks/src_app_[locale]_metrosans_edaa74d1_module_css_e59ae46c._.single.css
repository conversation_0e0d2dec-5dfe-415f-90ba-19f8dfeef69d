/* [project]/src/app/[locale]/metrosans_edaa74d1.module.css [app-client] (css) */
@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_Light-s.p.289ceb5b.ttf") format("truetype");
  font-display: swap;
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_LightItalic-s.p.41811076.ttf") format("truetype");
  font-display: swap;
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_Book-s.p.b7fcf052.ttf") format("truetype");
  font-display: swap;
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_BookItalic-s.p.541d50f2.ttf") format("truetype");
  font-display: swap;
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_Regular-s.p.ff98f09f.ttf") format("truetype");
  font-display: swap;
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_RegularItalic-s.p.834bb8de.ttf") format("truetype");
  font-display: swap;
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_Medium-s.p.d63e7211.ttf") format("truetype");
  font-display: swap;
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_MediumItalic-s.p.8bc1894e.ttf") format("truetype");
  font-display: swap;
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_SemiBold-s.p.682e6975.ttf") format("truetype");
  font-display: swap;
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_SemiBoldItalic-s.p.29f26aa3.ttf") format("truetype");
  font-display: swap;
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_Bold-s.p.edf554e9.ttf") format("truetype");
  font-display: swap;
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: metroSans;
  src: url("../media/MetroSans_BoldItalic-s.p.adeb239e.ttf") format("truetype");
  font-display: swap;
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: metroSans Fallback;
  src: local(Arial);
  ascent-override: 99.38%;
  descent-override: 19.9%;
  line-gap-override: 0.0%;
  size-adjust: 100.62%;
}

.metrosans_edaa74d1-module__VQh6Ga__className {
  font-family: metroSans, metroSans Fallback;
}

.metrosans_edaa74d1-module__VQh6Ga__variable {
  --font-metro-sans: "metroSans", "metroSans Fallback";
}

/*# sourceMappingURL=src_app_%5Blocale%5D_metrosans_edaa74d1_module_css_e59ae46c._.single.css.map*/