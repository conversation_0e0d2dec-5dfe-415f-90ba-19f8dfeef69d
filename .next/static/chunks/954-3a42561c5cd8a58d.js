"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{901:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=o(8229)._(o(2115)).default.createContext(null)},1193:(e,t)=>{function o(e){var t;let{config:o,src:r,width:i,quality:a}=e,n=a||(null==(t=o.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return o.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+n+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),o.__next_img_default=!0;let r=o},1469:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{default:function(){return s},getImageProps:function(){return l}});let r=o(8229),i=o(8883),a=o(3063),n=r._(o(1193));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,o]of Object.entries(t))void 0===o&&delete t[e];return{props:t}}let s=a.Image},2272:(e,t,o)=>{o.d(t,{A:()=>u});let r=`#version 300 es
precision highp float;
out vec4 fragColor;

uniform vec3 iResolution;
uniform float iTime;
uniform float iFrame;
uniform float timeScale;
uniform float hueShift;
uniform float saturation;
uniform float lightness;

#define POINTS 32
#define PI 3.1415926536
#define TAU (2.0 * PI)
#define S(a,b,t) smoothstep(a,b,t)

mat2 rot(float a) {
    float s = sin(a);
    float c = cos(a);
    return mat2(c, -s, s, c);
}

// HSV to RGB conversion
vec3 hsv2rgb(vec3 c) {
    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
    return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

// RGB to HSV conversion
vec3 rgb2hsv(vec3 c) {
    vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
    vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
    vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));

    float d = q.x - min(q.w, q.y);
    float e = 1.0e-10;
    return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

// Apply hue shift to RGB color
vec3 applyHueShift(vec3 color, float shift) {
    vec3 hsv = rgb2hsv(color);
    hsv.x = fract(hsv.x + shift); // Rotate hue by shift amount (0-1 range)
    return hsv2rgb(hsv);
}

// Apply saturation adjustment to RGB color
vec3 applySaturation(vec3 color, float satFactor) {
    vec3 hsv = rgb2hsv(color);
    hsv.y = clamp(hsv.y * satFactor, 0.0, 1.0); // Adjust saturation
    return hsv2rgb(hsv);
}

// Add dithering function
float dither(vec2 uv) {
    return fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
}

// Apply lightness adjustment to RGB color
vec3 applyLightness(vec3 color, float lightFactor) {
    // Convert to grayscale for more dramatic effect
    float gray = dot(color, vec3(0.299, 0.587, 0.114));

    // Shift the curve to make 0 match previous 1
    float shiftedFactor = (lightFactor * 14.0 + 1.0) / 15.0;
    float curve = shiftedFactor * shiftedFactor * 0.9;

    // Mix between original color and white/black based on lightness
    vec3 result;
    if (lightFactor > 0.5) {
        // Mix with white for lighter values, but cap at 0.95
        float mixAmount = min((curve - 0.5) * 2.0, 0.95);
        result = mix(color, vec3(1.0), mixAmount);
    } else {
        // Mix with black for darker values, but cap at 0.95
        float mixAmount = min(curve * 2.0, 0.95);
        result = mix(vec3(0.1), color, mixAmount);
    }

    // Add dithering to break up color bands
    float ditherAmount = (1.0 - lightFactor) * 0.02; // More dither in darker areas
    vec2 uv = gl_FragCoord.xy / iResolution.xy;
    float noise = dither(uv) * ditherAmount;
    result += vec3(noise);

    return result;
}

vec2 hash(vec2 p) {
    p = vec2(dot(p, vec2(2127.1, 81.17)), dot(p, vec2(1269.5, 283.37)));
    return fract(sin(p)*43758.5453);
}

float noise(in vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    vec2 u = f*f*(3.0-2.0*f);
    float n = mix(mix(dot(-1.0+2.0*hash(i + vec2(0.0, 0.0)), f - vec2(0.0, 0.0)),
    dot(-1.0+2.0*hash(i + vec2(1.0, 0.0)), f - vec2(1.0, 0.0)), u.x),
    mix(dot(-1.0+2.0*hash(i + vec2(0.0, 1.0)), f - vec2(0.0, 1.0)),
    dot(-1.0+2.0*hash(i + vec2(1.0, 1.0)), f - vec2(1.0, 1.0)), u.x), u.y);
    return 0.5 + 0.5*n;
}
`,i={a1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  vec2 tuv = uv - .5;
  float t = iTime * timeScale;  // Use timeScale for dynamic speed
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));  // Slow rotation
  tuv.y *= 1./aspectRatio;
  tuv *= rot(radians((degree-.5)*720.+180.));
  tuv.y *= aspectRatio;
  float frequency = 5.;
  float amplitude = 30.;
  float speed = t * 2.0;  // Use timeScale for speed
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 amberYellow = vec3(299, 186, 137) / vec3(255);
  vec3 deepBlue = vec3(49, 98, 238) / vec3(255);
  vec3 pink = vec3(246, 146, 146) / vec3(255);
  vec3 blue = vec3(89, 181, 243) / vec3(255);
  vec3 purpleHaze = vec3(105, 49, 245) / vec3(255);
  vec3 swampyBlack = vec3(32, 42, 50) / vec3(255);
  vec3 persimmonOrange = vec3(233, 51, 52) / vec3(255);
  vec3 darkAmber = vec3(233, 160, 75) / vec3(255);
  float cycle = sin(t * 0.5);  // Slower color cycling
  float mixT = (sign(cycle) * pow(abs(cycle), 0.6) + 1.) / 2.;
  vec3 color1 = mix(amberYellow, purpleHaze, mixT);
  vec3 color2 = mix(deepBlue, swampyBlack, mixT);
  vec3 color3 = mix(pink, persimmonOrange, mixT);
  vec3 color4 = mix(blue, darkAmber, mixT);
  vec3 layer1 = mix(color3, color2, smoothstep(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(color4, color1, smoothstep(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 color = mix(layer1, layer2, smoothstep(.5, -.3, tuv.y));

  // Apply hue shift to the final color
  color = applyHueShift(color, hueShift);

  // Apply saturation adjustment
  color = applySaturation(color, saturation);

  // Apply lightness adjustment
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,a2:`
vec3 hash3d(vec3 p) {
  p = vec3(dot(p, vec3(127.1, 311.7, 74.7)), dot(p, vec3(269.5, 183.3, 246.1)),
          dot(p, vec3(113.5, 271.9, 124.6)));
  p = -1.0 + 2.0 * fract(sin(p) * 43758.5453123);
  return p;
}

float noise3d(in vec3 p) {
  vec3 i = floor(p);
  vec3 f = fract(p);
  vec3 u = f * f * (3.0 - 2.0 * f);
  return mix(
      mix(mix(dot(hash3d(i + vec3(0.0, 0.0, 0.0)), f - vec3(0.0, 0.0, 0.0)),
              dot(hash3d(i + vec3(1.0, 0.0, 0.0)), f - vec3(1.0, 0.0, 0.0)),
              u.x),
          mix(dot(hash3d(i + vec3(0.0, 1.0, 0.0)), f - vec3(0.0, 1.0, 0.0)),
              dot(hash3d(i + vec3(1.0, 1.0, 0.0)), f - vec3(1.0, 1.0, 0.0)),
              u.x),
          u.y),
      mix(mix(dot(hash3d(i + vec3(0.0, 0.0, 1.0)), f - vec3(0.0, 0.0, 1.0)),
              dot(hash3d(i + vec3(1.0, 0.0, 1.0)), f - vec3(1.0, 0.0, 1.0)),
              u.x),
          mix(dot(hash3d(i + vec3(0.0, 1.0, 1.0)), f - vec3(0.0, 1.0, 1.0)),
              dot(hash3d(i + vec3(1.0, 1.0, 1.0)), f - vec3(1.0, 1.0, 1.0)),
              u.x),
          u.y),
      u.z);
}

vec4 shader(vec2 fragCoord) {
  const int layers = 5;
  const float baseSpeed = 0.25; // Base speed
  const float scale = 1.2;

  vec2 uv = (fragCoord - iResolution.xy - .5) / iResolution.y;
  float t = iTime * baseSpeed * timeScale; // Use timeScale for dynamic speed
  uv *= scale;
  float h =
      noise3d(vec3(uv * 2., t)); // Time as z-coordinate for continuous noise
  for (int n = 1; n < layers; n++) {
    float i = float(n);
    uv -= vec2(0.7 / i * sin(i * uv.y + i + t * 2.0 + h * i) +
                  0.8, // Reduced from 5.0 to 2.0
              0.4 / i * sin(uv.x + 4. - i + h + t * 2.0 + 0.3 * i) +
                  1.6); // Reduced from 5.0 to 2.0
  }
  uv -=
      vec2(1.2 * sin(uv.x + t + h) + 1.8, 0.4 * sin(uv.y + t + 0.3 * h) + 1.6);
  vec3 col = vec3(.5 * sin(uv.x) + 0.5, .5 * sin(uv.x + uv.y) + 0.5,
                  .5 * sin(uv.y) + 0.8) *
            0.8;

  // Apply hue shift to the final color
  col = applyHueShift(col, hueShift);

  // Apply saturation adjustment
  col = applySaturation(col, saturation);

  // Apply lightness adjustment
  col = applyLightness(col, lightness);

  return vec4(col, 1.0);
}
`,b1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord.xy / iResolution.xy;
  vec2 p[4];
  p[0] = vec2(0.1, 0.9);
  p[1] = vec2(0.9, 0.9);
  p[2] = vec2(0.5, 0.1);
  float t = iTime * timeScale;  // Use timeScale for dynamic speed
  p[3] = vec2(cos(t), sin(t)) * 0.4 + vec2(0.5, 0.5);
  vec3 c[4];
  // Add subtle color animation
  float colorShift = sin(t * 0.2) * 0.1;  // Slow color cycling
  c[0] = vec3(0.996078431372549 + colorShift, 0.3411764705882353, 0.33725490196078434);
  c[1] = vec3(0.996078431372549, 0.6352941176470588 + colorShift, 0.1607843137254902);
  c[2] = vec3(0.1450980392156863, 0.8196078431372549, 0.8588235294117647 + colorShift);
  c[3] = vec3(1.0, 1.0, 0.0);
  float blend = 2.0;
  vec3 sum = vec3(0.0);
  float valence = 0.0;
  for (int i = 0; i < 4; i++) {
      float distance = length(uv - p[i]);
      if (distance == 0.0) { distance = 1.0; }
      float w =  1.0 / pow(distance, blend);
      sum += w * c[i];
      valence += w;
  }
  sum /= valence;
  sum = pow(sum, vec3(1.0/2.2));

  // Apply hue shift to the final color
  sum = applyHueShift(sum, hueShift);

  // Apply saturation adjustment
  sum = applySaturation(sum, saturation);

  // Apply lightness adjustment
  sum = applyLightness(sum, lightness);

  return vec4(sum.xyz, 1.0);
}
`,b2:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float ratio = iResolution.x / iResolution.y;
  vec2 tuv = uv;
  tuv -= .5;
  float t = iTime * timeScale;
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));
  tuv.y *= 1./ratio;
  tuv *= rot(radians((degree-.5)*720.+180.));
  tuv.y *= ratio;
  float frequency = 5.;
  float amplitude = 30.;
  float speed = t * 1.0;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 colorYellow = vec3(.957, .804, .623);
  vec3 colorDeepBlue = vec3(.192, .384, .933);
  vec3 layer1 = mix(colorYellow, colorDeepBlue, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 colorRed = vec3(.910, .510, .8);
  vec3 colorBlue = vec3(0.350, .71, .953);
  vec3 layer2 = mix(colorRed, colorBlue, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 finalComp = mix(layer1, layer2, S(.5, -.3, tuv.y));

  // Apply color adjustments
  finalComp = applyHueShift(finalComp, hueShift);
  finalComp = applySaturation(finalComp, saturation);
  finalComp = applyLightness(finalComp, lightness);

  return vec4(finalComp, 1.0);
}
`,b3:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = (fragCoord/iResolution.xy)*1.;
  uv.y -= 1.5;
  uv.x += .2;
  float t = iTime * timeScale;  // Use timeScale uniform
  vec2 p = uv;
  float t1 = t * 1.5;  // Reduced from 3.0 to 1.5
  float t2 = t * 0.5;  // Reduced from 1.0 to 0.5
  p.y *= (p.x*p.y) * sin(p.y*p.x + t1);  // Reduced frequency from 2. to 1.
  float d = length(p*.7);
  vec3 c0 = vec3(1.);
  vec3 c1 = vec3(.365, .794, .935);
  vec3 c2 = vec3(.973, .671, .961);
  vec3 c3 = vec3(.973, .843, .439);
  float offset = 1.2;
  float step1 = .05*offset + sin(t2*2.)*.1;  // Reduced from 3. to 2.
  float step2 = 0.3*offset + sin(t2)*.15;
  float step3 = 0.6*offset + sin(t2)*.1;
  float step4 = 1.2*offset + sin(t2*2.)*.2;  // Reduced from 3. to 2.
  vec3 col = mix(c0, c1, smoothstep(step1, step2, d));
  col = mix(col, c2, smoothstep(step2, step3, d));
  col = mix(col, c3, smoothstep(step3, step4, d));

  // Apply color adjustments
  col = applyHueShift(col, hueShift);
  col = applySaturation(col, saturation);
  col = applyLightness(col, lightness);

  return vec4(col, .5);
}
`,b4:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float ratio = iResolution.x / iResolution.y;
  vec2 tuv = uv;
  tuv -= .5;
  float t = iTime * timeScale;
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));
  tuv.y *= 1./ratio;
  tuv *= rot(radians((degree-.5)*720.+75.));
  tuv.y *= ratio;
  float frequency = 2.;
  float amplitude = 30.;
  float speed = t * 1.0;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 colorWhite = vec3(1.0, 1.0, 1.0);
  vec3 colorRed = vec3(.914, .345, .62);
  vec3 colorPurple = vec3(.792, .573, .871);
  vec3 colorGreen = vec3(.612, .91, .364);
  vec3 colorBlue = vec3(.42, .773, .937);
  vec3 colorYellow = vec3(1.0, .973, .325);
  vec3 layer1 = mix(colorRed, colorYellow, S(-.6, .2, (tuv*rot(radians(-5.))).x));
  layer1 = mix(layer1, colorWhite, S(-.6, .2, (tuv*rot(radians(-5.))).x));
  layer1 = mix(layer1, colorPurple, S(-.2, .6, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(colorRed, colorYellow, S(-.8, .2, (tuv*rot(radians(-5.))).x));
  layer2 = mix(layer2, colorGreen, S(-.1, .9, (tuv*rot(radians(-5.))).x));
  layer2 = mix(layer2, colorBlue, S(-.5, .5, (tuv*rot(radians(-5.))).x));
  vec3 finalComp = mix(layer1, layer2, S(.7, -.5, tuv.y));

  // Apply color adjustments
  finalComp = applyHueShift(finalComp, hueShift);
  finalComp = applySaturation(finalComp, saturation);
  finalComp = applyLightness(finalComp, lightness);

  return vec4(finalComp, 1.0);
}
`,b5:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float t = iTime * timeScale;

  // Create smooth rotation based on noise
  float degree = noise(vec2(t * 0.1, uv.x*uv.y));
  vec2 tuv = uv * 2.0 - 1.0;
  tuv *= 1.5;
  tuv *= rot(radians((degree-.5)*720.+180.));

  // Add wave distortion with adjusted scaling
  float frequency = 3.0;
  float amplitude = 40.0;
  float speed = t * 0.8;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);

  // Define a rich color palette
  vec3 color1 = vec3(0.957, 0.804, 0.623);
  vec3 color2 = vec3(0.192, 0.384, 0.933);
  vec3 color3 = vec3(0.910, 0.510, 0.800);
  vec3 color4 = vec3(0.350, 0.710, 0.953);

  // Create layered gradients with smooth transitions
  vec3 layer1 = mix(color1, color2, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(color3, color4, S(-.3, .2, (tuv*rot(radians(-5.))).x));

  // Blend layers with smooth vertical transition
  vec3 finalColor = mix(layer1, layer2, S(.5, -.3, tuv.y));

  // Add subtle color variation based on time
  float colorShift = sin(t * 0.3) * 0.1;
  finalColor = mix(finalColor, finalColor.yzx, colorShift);

  // Add subtle vignette with wider coverage
  float vignette = smoothstep(1.0, 0.0, length(uv - 0.5));
  finalColor *= vignette;

  // Apply color adjustments
  finalColor = applyHueShift(finalColor, hueShift);
  finalColor = applySaturation(finalColor, saturation);
  finalColor = applyLightness(finalColor, lightness);

  return vec4(finalColor, 1.0);
}
`,f1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create fluid-like movement with aspect ratio correction
  vec2 p = uv * 2.0 - 1.0;
  p.x *= aspectRatio;
  p *= 0.5; // Reduced scale for larger blobs

  // Add some noise-based distortion with lower frequency
  float noise1 = noise(p + t * 0.05); // Reduced time factor
  float noise2 = noise(p * 0.5 - t * 0.1); // Reduced scale and time factor

  // Create gradient with noise influence
  vec3 color = vec3(
    noise1 * 0.5 + 0.5,
    noise2 * 0.5 + 0.5,
    (noise1 + noise2) * 0.5
  );

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,f2:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create multiple layers of fluid movement with aspect ratio correction
  vec2 p1 = uv * 1.0;
  p1.x *= aspectRatio;
  vec2 p2 = uv * 1.5;
  p2.x *= aspectRatio;

  // Generate noise at different scales with lower frequency
  float noise1 = noise(p1 + t * 0.05);
  float noise2 = noise(p2 - t * 0.08);
  float noise3 = noise(p1 * 0.5 + t * 0.1);

  // Combine noise layers
  float combinedNoise = (noise1 + noise2 + noise3) / 3.0;

  // Create color based on noise
  vec3 color = vec3(
    noise1 * 0.7 + 0.3,
    noise2 * 0.7 + 0.3,
    combinedNoise * 0.7 + 0.3
  );

  // Add some rotation-based variation
  vec2 rotatedUV = uv * rot(t * 0.05);
  rotatedUV.x *= aspectRatio;
  float rotationNoise = noise(rotatedUV * 0.5);
  color = mix(color, color.yzx, rotationNoise * 0.3);

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,f3:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create abstract fluid movement with aspect ratio correction
  vec2 p = uv * 1.0;
  p.x *= aspectRatio;
  p = p * rot(t * 0.02);

  // Generate multiple noise layers with lower frequency
  float noise1 = noise(p + t * 0.05);
  float noise2 = noise(p * 0.5 - t * 0.08);
  float noise3 = noise(p * 0.25 + t * 0.1);

  // Create color channels with different noise combinations
  vec3 color = vec3(
    noise1 * noise2,
    noise2 * noise3,
    noise3 * noise1
  );

  // Add some movement-based variation
  vec2 movement = vec2(sin(t * 0.1), cos(t * 0.15)) * 0.2;
  movement.x *= aspectRatio;
  float movementNoise = noise(uv + movement);
  color = mix(color, color.zxy, movementNoise);

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,n1:`
vec3 irri(float hue) {
    return 0.5 + 0.5 * cos((9.0 * hue) + vec3(0.0, 23.0, 21.0));
}

vec2 line(vec2 p, vec2 a, vec2 b) {
    vec2 ba = b - a;
    vec2 pa = p - a;
    float h = clamp(dot(pa, ba) / dot(ba, ba), 0.0, 1.0);
    return vec2(length(pa - h * ba), h);
}

vec4 shader(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float aspectRatio = iResolution.x / iResolution.y;
    float t = iTime * timeScale;

    // Create noise-based gradient with aspect ratio correction
    vec2 p = uv * 0.5;
    p.x *= aspectRatio;
    float noise1 = noise(p + t * 0.05);
    float noise2 = noise(p * 0.5 - t * 0.08);

    // Create gradient with noise influence
    vec3 color = vec3(
        noise1 * 0.8 + 0.2,
        noise2 * 0.8 + 0.2,
        (noise1 + noise2) * 0.4 + 0.3
    );

    // Add some rotation-based variation
    vec2 rotatedUV = uv * rot(t * 0.02);
    rotatedUV.x *= aspectRatio;
    float rotationNoise = noise(rotatedUV * 0.5);
    color = mix(color, color.yzx, rotationNoise * 0.2);

    // Apply color adjustments
    color = applyHueShift(color, hueShift);
    color = applySaturation(color, saturation);
    color = applyLightness(color, lightness);

    return vec4(color, 1.0);
}
`,n2:`
vec4 shader(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float aspectRatio = iResolution.x / iResolution.y;
    float t = iTime * timeScale;

    // Create complex noise pattern with aspect ratio correction
    vec2 p1 = uv * 0.5;
    p1.x *= aspectRatio;
    vec2 p2 = uv * 0.75;
    p2.x *= aspectRatio;

    // Generate multiple noise layers with lower frequency
    float noise1 = noise(p1 + t * 0.05);
    float noise2 = noise(p2 - t * 0.08);
    float noise3 = noise(p1 * 0.25 + t * 0.1);

    // Combine noise layers with different weights
    float combinedNoise = (noise1 * 0.4 + noise2 * 0.3 + noise3 * 0.3);

    // Create color with noise influence
    vec3 color = vec3(
        noise1 * 0.6 + 0.4,
        noise2 * 0.6 + 0.4,
        combinedNoise * 0.6 + 0.4
    );

    // Add some movement-based variation
    vec2 movement = vec2(sin(t * 0.1), cos(t * 0.15)) * 0.2;
    movement.x *= aspectRatio;
    float movementNoise = noise(uv + movement);
    color = mix(color, color.zxy, movementNoise * 0.3);

    // Apply color adjustments
    color = applyHueShift(color, hueShift);
    color = applySaturation(color, saturation);
    color = applyLightness(color, lightness);

    return vec4(color, 1.0);
}
`};Object.keys(i);let a=e=>Math.round(17*Number.parseInt(e,16)),n=(e,t,o,r=2)=>{let i=Math.max(0,Math.min(e,15));return 0===i?t:t+((i-1)/14)**r*(o-t)},l=`#version 300 es
      in vec2 position;
      void main() {
          gl_Position = vec4(position, 0.0, 1.0);
      }`;class s{#e;#t;#o;#r;#i;#a;#n;#l;#s;#c;#d;#u;#f;static #m=50;constructor(e,t,o){this.#t=e,this.#c=t,this.#d=l,this.#i=.4,this.#a=!1,this.#l=o,this.#n=o[1],this.#s={speed:0,hueShift:0,saturation:0,lightness:0},this.#u=null,this.#f=0,this.#p()}#p(){this.#t.addEventListener("webglcontextlost",e=>{e.preventDefault(),this.#a=!1,this.#e=null,this.#o=null,this.#r=null,this.#t&&(this.#t.width=0)}),this.#t.addEventListener("webglcontextrestored",()=>{this.init()})}init(){this.#e=this.#h(this.#t),this.#o=this.#v(this.#d,this.#c),this.#r=this.#g(),this.#a=!0,this.#b(),this.#y(),this.#x(!0),this.#w()}#h(e){let t=e.getContext("webgl2",{antialias:!0});if(!t)throw Error("WebGL2 not supported");return t}#k(e,t){let o=this.#e.createShader(e);this.#e.shaderSource(o,t),this.#e.compileShader(o);let r=this.#e.getShaderInfoLog(o);if(r)throw Error(`${e===this.#e.VERTEX_SHADER?"Vertex":"Fragment"} shader compilation error: ${r}`);return o}#v(e,t){let o=this.#e.createProgram(),r=this.#k(this.#e.VERTEX_SHADER,e),i=this.#k(this.#e.FRAGMENT_SHADER,t);this.#e.attachShader(o,r),this.#e.attachShader(o,i),this.#e.linkProgram(o);let a=this.#e.getProgramInfoLog(o);return a&&console.error("Program linking error:",a),this.#e.detachShader(o,r),this.#e.detachShader(o,i),this.#e.deleteShader(r),this.#e.deleteShader(i),this.#e.useProgram(o),o}#b(){let e=this.#e.createBuffer();this.#e.bindBuffer(this.#e.ARRAY_BUFFER,e),this.#e.bufferData(this.#e.ARRAY_BUFFER,new Float32Array([-1,-1,1,-1,-1,1,1,1]),this.#e.STATIC_DRAW)}#y(){let e=this.#e.getAttribLocation(this.#o,"position");this.#e.enableVertexAttribArray(e),this.#e.vertexAttribPointer(e,2,this.#e.FLOAT,!1,0,0)}#g(){return{iResolution:this.#e.getUniformLocation(this.#o,"iResolution"),iTime:this.#e.getUniformLocation(this.#o,"iTime"),iFrame:this.#e.getUniformLocation(this.#o,"iFrame"),options:this.#e.getUniformLocation(this.#o,"options"),timeScale:this.#e.getUniformLocation(this.#o,"timeScale"),hueShift:this.#e.getUniformLocation(this.#o,"hueShift"),saturation:this.#e.getUniformLocation(this.#o,"saturation"),lightness:this.#e.getUniformLocation(this.#o,"lightness")}}#x(e=!1){if(!this.#n)return;this.#e.useProgram(this.#o),this.#e.uniform1iv(this.#r.options,this.#n);let[t,o,r,i]=this.#n.map(e=>Math.round(15*e/255)),[a,l,s,c]=[n(t,.1,3,1.5),o/15,n(r,.3,3,1.5),i/15];(e||a!==this.#s.speed||l!==this.#s.hueShift||s!==this.#s.saturation||c!==this.#s.lightness)&&(this.#e.uniform1f(this.#r.timeScale,a),this.#e.uniform1f(this.#r.hueShift,l),this.#e.uniform1f(this.#r.saturation,s),this.#e.uniform1f(this.#r.lightness,c),this.#s={speed:a,hueShift:l,saturation:s,lightness:c})}updateSeed(e){return!(e[0]===this.#l[0]&&e[1].every((e,t)=>e===this.#l[1][t]))&&(this.#l=e,this.#n=e[1],this.#x(!0),!0)}#S(e){if(!this.#a||!this.#t||!this.#e)return;let{iResolution:t,iTime:o,iFrame:r}=this.#r;this.#e.useProgram(this.#o);let i=this.#t.clientWidth,a=this.#t.clientHeight;(this.#t.width!==i||this.#t.height!==a)&&(this.#t.width=i,this.#t.height=a,this.#e.uniform3f(t,this.#t.width,this.#t.height,1),this.#e.viewport(0,0,this.#t.width,this.#t.height));let n=e/1e3;this.#e.uniform1f(o,n),this.#e.uniform1f(r,Math.floor(60*n))}#C(e){if(null!==this.#u){let t=Math.min(e-this.#u,s.#m);this.#f+=t}this.#u=e}#w(){let e=()=>{if(!this.#a||!this.#t||!this.#e)return;let t=performance.now();this.#C(t),this.#S(this.#f),this.#e.drawArrays(this.#e.TRIANGLE_STRIP,0,4),requestAnimationFrame(e)};requestAnimationFrame(e)}destroy(){if(this.#a=!1,this.#o&&this.#e&&this.#e.deleteProgram(this.#o),this.#t)try{this.#t.remove()}catch(e){}this.#o=null,this.#t=null,this.#e=null}}let c=`
  void main() {
    fragColor = shader(gl_FragCoord.xy);
  }
  `,d=null;async function u(e,t="body"){if(!e)throw Error("Seed is required");let o=[e.split(".").shift(),new Uint8Array(e.split(".").pop().split("").map(a))],[n]=o;if(d?.shaderId===n)return d.updateSeed(o),d;d&&(d.destroy(),d=null);let[l,f]=await Promise.all([Promise.resolve(r),Promise.resolve(i[n])]),m=new s(((e="body")=>{let t=document.querySelector(e)??document.body;return"CANVAS"===t.tagName?t:t.appendChild(Object.assign(document.createElement("canvas"),{id:"gradient-gl",style:"position:fixed;inset:0;width:100vw;height:100vh;z-index:-1;pointer-events:none;"}))})(t),l+f+c,o);return m.shaderId=n,m.init(),d=m,m}let f=new URL("file:///home/<USER>/Projects/AlbatrosDoc/node_modules/gradient-gl/index.js"),m=f.searchParams.get("seed"),p=f.searchParams.get("selector")||"body";m&&u(m,p)},2464:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=o(8229)._(o(2115)).default.createContext({})},2596:(e,t,o)=>{o.d(t,{$:()=>r});function r(){for(var e,t,o=0,r="",i=arguments.length;o<i;o++)(e=arguments[o])&&(t=function e(t){var o,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(o=0;o<a;o++)t[o]&&(r=e(t[o]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},3063:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let r=o(8229),i=o(6966),a=o(5155),n=i._(o(2115)),l=r._(o(7650)),s=r._(o(5564)),c=o(8883),d=o(5840),u=o(6752);o(3230);let f=o(901),m=r._(o(1193)),p=o(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function v(e,t,o,r,i,a,n){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==o?void 0:o.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,i=!1;o.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function g(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}let b=(0,n.forwardRef)((e,t)=>{let{src:o,srcSet:r,sizes:i,height:l,width:s,decoding:c,className:d,style:u,fetchPriority:f,placeholder:m,loading:h,unoptimized:b,fill:y,onLoadRef:x,onLoadingCompleteRef:w,setBlurComplete:k,setShowAltText:S,sizesInput:C,onLoad:R,onError:_,...j}=e,z=(0,n.useCallback)(e=>{e&&(_&&(e.src=e.src),e.complete&&v(e,m,x,w,k,b,C))},[o,m,x,w,k,_,b,C]),A=(0,p.useMergedRef)(t,z);return(0,a.jsx)("img",{...j,...g(f),loading:h,width:s,height:l,decoding:c,"data-nimg":y?"fill":"1",className:d,style:u,sizes:i,srcSet:r,src:o,ref:A,onLoad:e=>{v(e.currentTarget,m,x,w,k,b,C)},onError:e=>{S(!0),"empty"!==m&&k(!0),_&&_(e)}})});function y(e){let{isAppRouter:t,imgAttributes:o}=e,r={as:"image",imageSrcSet:o.srcSet,imageSizes:o.sizes,crossOrigin:o.crossOrigin,referrerPolicy:o.referrerPolicy,...g(o.fetchPriority)};return t&&l.default.preload?(l.default.preload(o.src,r),null):(0,a.jsx)(s.default,{children:(0,a.jsx)("link",{rel:"preload",href:o.srcSet?void 0:o.src,...r},"__nimg-"+o.src+o.srcSet+o.sizes)})}let x=(0,n.forwardRef)((e,t)=>{let o=(0,n.useContext)(f.RouterContext),r=(0,n.useContext)(u.ImageConfigContext),i=(0,n.useMemo)(()=>{var e;let t=h||r||d.imageConfigDefault,o=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:o,deviceSizes:i,qualities:a}},[r]),{onLoad:l,onLoadingComplete:s}=e,p=(0,n.useRef)(l);(0,n.useEffect)(()=>{p.current=l},[l]);let v=(0,n.useRef)(s);(0,n.useEffect)(()=>{v.current=s},[s]);let[g,x]=(0,n.useState)(!1),[w,k]=(0,n.useState)(!1),{props:S,meta:C}=(0,c.getImgProps)(e,{defaultLoader:m.default,imgConf:i,blurComplete:g,showAltText:w});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{...S,unoptimized:C.unoptimized,placeholder:C.placeholder,fill:C.fill,onLoadRef:p,onLoadingCompleteRef:v,setBlurComplete:x,setShowAltText:k,sizesInput:e.sizes,ref:t}),C.priority?(0,a.jsx)(y,{isAppRouter:!o,imgAttributes:S}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5029:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let r=o(2115),i=r.useLayoutEffect,a=r.useEffect;function n(e){let{headManager:t,reduceComponentsToState:o}=e;function n(){if(t&&t.mountedInstances){let i=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(o(i,e))}}return i(()=>{var o;return null==t||null==(o=t.mountedInstances)||o.add(e.children),()=>{var o;null==t||null==(o=t.mountedInstances)||o.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=n),()=>{t&&(t._pendingUpdate=n)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function o(e){let{widthInt:t,heightInt:o,blurWidth:r,blurHeight:i,blurDataURL:a,objectFit:n}=e,l=r?40*r:t,s=i?40*i:o,c=l&&s?"viewBox='0 0 "+l+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return o}})},5564:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{default:function(){return h},defaultHead:function(){return u}});let r=o(8229),i=o(6966),a=o(5155),n=i._(o(2115)),l=r._(o(5029)),s=o(2464),c=o(2830),d=o(7544);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}o(3230);let m=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:o}=t;return e.reduce(f,[]).reverse().concat(u(o).reverse()).filter(function(){let e=new Set,t=new Set,o=new Set,r={};return i=>{let a=!0,n=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){n=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?a=!1:t.add(i.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(i.props.hasOwnProperty(t))if("charSet"===t)o.has(t)?a=!1:o.add(t);else{let e=i.props[t],o=r[t]||new Set;("name"!==t||!n)&&o.has(e)?a=!1:(o.add(e),r[t]=o)}}}return a}}()).reverse().map((e,t)=>{let o=e.key||t;return n.default.cloneElement(e,{key:o})})}let h=function(e){let{children:t}=e,o=(0,n.useContext)(s.AmpStateContext),r=(0,n.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,d.isInAmpMode)(o),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5695:(e,t,o)=>{var r=o(8999);o.o(r,"usePathname")&&o.d(t,{usePathname:function(){return r.usePathname}}),o.o(r,"useRouter")&&o.d(t,{useRouter:function(){return r.useRouter}})},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{VALID_LOADERS:function(){return o},imageConfigDefault:function(){return r}});let o=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6654:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=o(2115);function i(e,t){let o=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=o.current;e&&(o.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(o.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let o=e(t);return"function"==typeof o?o:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6752:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let r=o(8229)._(o(2115)),i=o(5840),a=r.default.createContext(i.imageConfigDefault)},6766:(e,t,o)=>{o.d(t,{default:()=>i.a});var r=o(1469),i=o.n(r)},7544:(e,t)=>{function o(e){let{ampFirst:t=!1,hybrid:o=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||o&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return o}})},7652:(e,t,o)=>{o.d(t,{c3:()=>a});var r=o(2550);function i(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let a=i(0,r.c3);i(0,r.kc)},8883:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),o(3230);let r=o(5100),i=o(5840),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var o,s;let c,d,u,{src:f,sizes:m,unoptimized:p=!1,priority:h=!1,loading:v,className:g,quality:b,width:y,height:x,fill:w=!1,style:k,overrideSrc:S,onLoad:C,onLoadingComplete:R,placeholder:_="empty",blurDataURL:j,fetchPriority:z,decoding:A="async",layout:P,objectFit:E,objectPosition:O,lazyBoundary:M,lazyRoot:T,...U}=e,{imgConf:L,showAltText:I,blurComplete:N,defaultLoader:q}=t,F=L||i.imageConfigDefault;if("allSizes"in F)c=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),r=null==(o=F.qualities)?void 0:o.sort((e,t)=>e-t);c={...F,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===q)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=U.loader||q;delete U.loader,delete U.srcSet;let B="__next_img_default"in G;if(B){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:o,...r}=t;return e(r)}}if(P){"fill"===P&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(k={...k,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!m&&(m=t)}let D="",H=l(y),V=l(x);if((s=f)&&"object"==typeof s&&(n(s)||void 0!==s.src)){let e=n(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,u=e.blurHeight,j=j||e.blurDataURL,D=e.src,!w)if(H||V){if(H&&!V){let t=H/e.width;V=Math.round(e.height*t)}else if(!H&&V){let t=V/e.height;H=Math.round(e.width*t)}}else H=e.width,V=e.height}let W=!h&&("lazy"===v||void 0===v);(!(f="string"==typeof f?f:D)||f.startsWith("data:")||f.startsWith("blob:"))&&(p=!0,W=!1),c.unoptimized&&(p=!0),B&&!c.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(p=!0);let $=l(b),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:O}:{},I?{}:{color:"transparent"},k),K=N||"empty"===_?null:"blur"===_?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:H,heightInt:V,blurWidth:d,blurHeight:u,blurDataURL:j||"",objectFit:Y.objectFit})+'")':'url("'+_+'")',X=a.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,J=K?{backgroundSize:X,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Q=function(e){let{config:t,src:o,unoptimized:r,width:i,quality:a,sizes:n,loader:l}=e;if(r)return{src:o,srcSet:void 0,sizes:void 0};let{widths:s,kind:c}=function(e,t,o){let{deviceSizes:r,allSizes:i}=e;if(o){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(o);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,n),d=s.length-1;return{sizes:n||"w"!==c?n:"100vw",srcSet:s.map((e,r)=>l({config:t,src:o,quality:a,width:e})+" "+("w"===c?e:r+1)+c).join(", "),src:l({config:t,src:o,quality:a,width:s[d]})}}({config:c,src:f,unoptimized:p,width:H,quality:$,sizes:m,loader:G});return{props:{...U,loading:W?"lazy":v,fetchPriority:z,width:H,height:V,decoding:A,className:g,style:{...Y,...J},sizes:Q.sizes,srcSet:Q.srcSet,src:S||Q.src},meta:{unoptimized:p,priority:h,placeholder:_,fill:w}}}},9688:(e,t,o)=>{o.d(t,{QP:()=>ee});let r=(e,t)=>{if(0===e.length)return t.classGroupId;let o=e[0],i=t.nextPart.get(o),a=i?r(e.slice(1),i):void 0;if(a)return a;if(0===t.validators.length)return;let n=e.join("-");return t.validators.find(({validator:e})=>e(n))?.classGroupId},i=/^\[(.+)\]$/,a=(e,t,o,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:n(t,e)).classGroupId=o;return}if("function"==typeof e)return l(e)?void a(e(r),t,o,r):void t.validators.push({validator:e,classGroupId:o});Object.entries(e).forEach(([e,i])=>{a(i,n(t,e),o,r)})})},n=(e,t)=>{let o=e;return t.split("-").forEach(e=>{o.nextPart.has(e)||o.nextPart.set(e,{nextPart:new Map,validators:[]}),o=o.nextPart.get(e)}),o},l=e=>e.isThemeGetter,s=/\s+/;function c(){let e,t,o=0,r="";for(;o<arguments.length;)(e=arguments[o++])&&(t=d(e))&&(r&&(r+=" "),r+=t);return r}let d=e=>{let t;if("string"==typeof e)return e;let o="";for(let r=0;r<e.length;r++)e[r]&&(t=d(e[r]))&&(o&&(o+=" "),o+=t);return o},u=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},f=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,m=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,h=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,b=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>p.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),S=e=>e.endsWith("%")&&w(e.slice(0,-1)),C=e=>h.test(e),R=()=>!0,_=e=>v.test(e)&&!g.test(e),j=()=>!1,z=e=>b.test(e),A=e=>y.test(e),P=e=>!O(e)&&!N(e),E=e=>V(e,K,j),O=e=>f.test(e),M=e=>V(e,X,_),T=e=>V(e,J,w),U=e=>V(e,$,j),L=e=>V(e,Y,A),I=e=>V(e,Z,z),N=e=>m.test(e),q=e=>W(e,X),F=e=>W(e,Q),G=e=>W(e,$),B=e=>W(e,K),D=e=>W(e,Y),H=e=>W(e,Z,!0),V=(e,t,o)=>{let r=f.exec(e);return!!r&&(r[1]?t(r[1]):o(r[2]))},W=(e,t,o=!1)=>{let r=m.exec(e);return!!r&&(r[1]?t(r[1]):o)},$=e=>"position"===e||"percentage"===e,Y=e=>"image"===e||"url"===e,K=e=>"length"===e||"size"===e||"bg-size"===e,X=e=>"length"===e,J=e=>"number"===e,Q=e=>"family-name"===e,Z=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let o,n,l,d=function(s){let c;return n=(o={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,o=new Map,r=new Map,i=(i,a)=>{o.set(i,a),++t>e&&(t=0,r=o,o=new Map)};return{get(e){let t=o.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){o.has(e)?o.set(e,t):i(e,t)}}})((c=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:o}=e,r=e=>{let t,o,r=[],i=0,a=0,n=0;for(let o=0;o<e.length;o++){let l=e[o];if(0===i&&0===a){if(":"===l){r.push(e.slice(n,o)),n=o+1;continue}if("/"===l){t=o;continue}}"["===l?i++:"]"===l?i--:"("===l?a++:")"===l&&a--}let l=0===r.length?e:e.substring(n),s=(o=l).endsWith("!")?o.substring(0,o.length-1):o.startsWith("!")?o.substring(1):o;return{modifiers:r,hasImportantModifier:s!==l,baseClassName:s,maybePostfixModifierPosition:t&&t>n?t-n:void 0}};if(t){let e=t+":",o=r;r=t=>t.startsWith(e)?o(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(o){let e=r;r=t=>o({className:t,parseClassName:e})}return r})(c),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let o=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(o.push(...r.sort(),e),r=[]):r.push(e)}),o.push(...r.sort()),o}})(c),...(e=>{let t=(e=>{let{theme:t,classGroups:o}=e,r={nextPart:new Map,validators:[]};for(let e in o)a(o[e],r,e,t);return r})(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let o=e.split("-");return""===o[0]&&1!==o.length&&o.shift(),r(o,t)||(e=>{if(i.test(e)){let t=i.exec(e)[1],o=t?.substring(0,t.indexOf(":"));if(o)return"arbitrary.."+o}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=o[e]||[];return t&&n[e]?[...r,...n[e]]:r}}})(c)}).cache.get,l=o.cache.set,d=u,u(s)};function u(e){let t=n(e);if(t)return t;let r=((e,t)=>{let{parseClassName:o,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:a}=t,n=[],l=e.trim().split(s),c="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:s,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:m}=o(t);if(s){c=t+(c.length>0?" "+c:c);continue}let p=!!m,h=r(p?f.substring(0,m):f);if(!h){if(!p||!(h=r(f))){c=t+(c.length>0?" "+c:c);continue}p=!1}let v=a(d).join(":"),g=u?v+"!":v,b=g+h;if(n.includes(b))continue;n.push(b);let y=i(h,p);for(let e=0;e<y.length;++e){let t=y[e];n.push(g+t)}c=t+(c.length>0?" "+c:c)}return c})(e,o);return l(e,r),r}return function(){return d(c.apply(null,arguments))}}(()=>{let e=u("color"),t=u("font"),o=u("text"),r=u("font-weight"),i=u("tracking"),a=u("leading"),n=u("breakpoint"),l=u("container"),s=u("spacing"),c=u("radius"),d=u("shadow"),f=u("inset-shadow"),m=u("text-shadow"),p=u("drop-shadow"),h=u("blur"),v=u("perspective"),g=u("aspect"),b=u("ease"),y=u("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],z=()=>[...j(),N,O],A=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto","contain","none"],W=()=>[N,O,s],$=()=>[x,"full","auto",...W()],Y=()=>[k,"none","subgrid",N,O],K=()=>["auto",{span:["full",k,N,O]},k,N,O],X=()=>[k,"auto",N,O],J=()=>["auto","min","max","fr",N,O],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Z=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...W()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...W()],eo=()=>[e,N,O],er=()=>[...j(),G,U,{position:[N,O]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",B,E,{size:[N,O]}],en=()=>[S,q,M],el=()=>["","none","full",c,N,O],es=()=>["",w,q,M],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[w,S,G,U],ef=()=>["","none",h,N,O],em=()=>["none",w,N,O],ep=()=>["none",w,N,O],eh=()=>[w,N,O],ev=()=>[x,"full",...W()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[R],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[P],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",w],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,O,N,g]}],container:["container"],columns:[{columns:[w,O,N,l]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:z()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:V()}],"overscroll-x":[{"overscroll-x":V()}],"overscroll-y":[{"overscroll-y":V()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:$()}],"inset-x":[{"inset-x":$()}],"inset-y":[{"inset-y":$()}],start:[{start:$()}],end:[{end:$()}],top:[{top:$()}],right:[{right:$()}],bottom:[{bottom:$()}],left:[{left:$()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",N,O]}],basis:[{basis:[x,"full","auto",l,...W()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",O]}],grow:[{grow:["",w,N,O]}],shrink:[{shrink:["",w,N,O]}],order:[{order:[k,"first","last","none",N,O]}],"grid-cols":[{"grid-cols":Y()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":Y()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":J()}],"auto-rows":[{"auto-rows":J()}],gap:[{gap:W()}],"gap-x":[{"gap-x":W()}],"gap-y":[{"gap-y":W()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...Z(),"normal"]}],"justify-self":[{"justify-self":["auto",...Z()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...Z(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Z(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...Z(),"baseline"]}],"place-self":[{"place-self":["auto",...Z()]}],p:[{p:W()}],px:[{px:W()}],py:[{py:W()}],ps:[{ps:W()}],pe:[{pe:W()}],pt:[{pt:W()}],pr:[{pr:W()}],pb:[{pb:W()}],pl:[{pl:W()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":W()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":W()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",o,q,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,N,T]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",S,O]}],"font-family":[{font:[F,O,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,N,O]}],"line-clamp":[{"line-clamp":[w,"none",N,T]}],leading:[{leading:[a,...W()]}],"list-image":[{"list-image":["none",N,O]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",N,O]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:eo()}],"text-color":[{text:eo()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",N,M]}],"text-decoration-color":[{decoration:eo()}],"underline-offset":[{"underline-offset":[w,"auto",N,O]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:W()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N,O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N,O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,N,O],radial:["",N,O],conic:[k,N,O]},D,L]}],"bg-color":[{bg:eo()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:eo()}],"gradient-via":[{via:eo()}],"gradient-to":[{to:eo()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:eo()}],"border-color-x":[{"border-x":eo()}],"border-color-y":[{"border-y":eo()}],"border-color-s":[{"border-s":eo()}],"border-color-e":[{"border-e":eo()}],"border-color-t":[{"border-t":eo()}],"border-color-r":[{"border-r":eo()}],"border-color-b":[{"border-b":eo()}],"border-color-l":[{"border-l":eo()}],"divide-color":[{divide:eo()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,N,O]}],"outline-w":[{outline:["",w,q,M]}],"outline-color":[{outline:eo()}],shadow:[{shadow:["","none",d,H,I]}],"shadow-color":[{shadow:eo()}],"inset-shadow":[{"inset-shadow":["none",f,H,I]}],"inset-shadow-color":[{"inset-shadow":eo()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:eo()}],"ring-offset-w":[{"ring-offset":[w,M]}],"ring-offset-color":[{"ring-offset":eo()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":eo()}],"text-shadow":[{"text-shadow":["none",m,H,I]}],"text-shadow-color":[{"text-shadow":eo()}],opacity:[{opacity:[w,N,O]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":eo()}],"mask-image-linear-to-color":[{"mask-linear-to":eo()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":eo()}],"mask-image-t-to-color":[{"mask-t-to":eo()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":eo()}],"mask-image-r-to-color":[{"mask-r-to":eo()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":eo()}],"mask-image-b-to-color":[{"mask-b-to":eo()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":eo()}],"mask-image-l-to-color":[{"mask-l-to":eo()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":eo()}],"mask-image-x-to-color":[{"mask-x-to":eo()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":eo()}],"mask-image-y-to-color":[{"mask-y-to":eo()}],"mask-image-radial":[{"mask-radial":[N,O]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":eo()}],"mask-image-radial-to-color":[{"mask-radial-to":eo()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":eo()}],"mask-image-conic-to-color":[{"mask-conic-to":eo()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",N,O]}],filter:[{filter:["","none",N,O]}],blur:[{blur:ef()}],brightness:[{brightness:[w,N,O]}],contrast:[{contrast:[w,N,O]}],"drop-shadow":[{"drop-shadow":["","none",p,H,I]}],"drop-shadow-color":[{"drop-shadow":eo()}],grayscale:[{grayscale:["",w,N,O]}],"hue-rotate":[{"hue-rotate":[w,N,O]}],invert:[{invert:["",w,N,O]}],saturate:[{saturate:[w,N,O]}],sepia:[{sepia:["",w,N,O]}],"backdrop-filter":[{"backdrop-filter":["","none",N,O]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[w,N,O]}],"backdrop-contrast":[{"backdrop-contrast":[w,N,O]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,N,O]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,N,O]}],"backdrop-invert":[{"backdrop-invert":["",w,N,O]}],"backdrop-opacity":[{"backdrop-opacity":[w,N,O]}],"backdrop-saturate":[{"backdrop-saturate":[w,N,O]}],"backdrop-sepia":[{"backdrop-sepia":["",w,N,O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":W()}],"border-spacing-x":[{"border-spacing-x":W()}],"border-spacing-y":[{"border-spacing-y":W()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",N,O]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",N,O]}],ease:[{ease:["linear","initial",b,N,O]}],delay:[{delay:[w,N,O]}],animate:[{animate:["none",y,N,O]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,N,O]}],"perspective-origin":[{"perspective-origin":z()}],rotate:[{rotate:em()}],"rotate-x":[{"rotate-x":em()}],"rotate-y":[{"rotate-y":em()}],"rotate-z":[{"rotate-z":em()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[N,O,"","none","gpu","cpu"]}],"transform-origin":[{origin:z()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:eo()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:eo()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N,O]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":W()}],"scroll-mx":[{"scroll-mx":W()}],"scroll-my":[{"scroll-my":W()}],"scroll-ms":[{"scroll-ms":W()}],"scroll-me":[{"scroll-me":W()}],"scroll-mt":[{"scroll-mt":W()}],"scroll-mr":[{"scroll-mr":W()}],"scroll-mb":[{"scroll-mb":W()}],"scroll-ml":[{"scroll-ml":W()}],"scroll-p":[{"scroll-p":W()}],"scroll-px":[{"scroll-px":W()}],"scroll-py":[{"scroll-py":W()}],"scroll-ps":[{"scroll-ps":W()}],"scroll-pe":[{"scroll-pe":W()}],"scroll-pt":[{"scroll-pt":W()}],"scroll-pr":[{"scroll-pr":W()}],"scroll-pb":[{"scroll-pb":W()}],"scroll-pl":[{"scroll-pl":W()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N,O]}],fill:[{fill:["none",...eo()]}],"stroke-w":[{stroke:[w,q,M,T]}],stroke:[{stroke:["none",...eo()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}}]);