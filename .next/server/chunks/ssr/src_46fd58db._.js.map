{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/[locale]/metrosans_edaa74d1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"metrosans_edaa74d1-module__VQh6Ga__className\",\n  \"variable\": \"metrosans_edaa74d1-module__VQh6Ga__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/app/%5Blocale%5D/metrosans_edaa74d1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:[{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-Light.ttf%22,%22weight%22:%22300%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-LightItalic.ttf%22,%22weight%22:%22300%22,%22style%22:%22italic%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-Book.ttf%22,%22weight%22:%22400%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-BookItalic.ttf%22,%22weight%22:%22400%22,%22style%22:%22italic%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-Regular.ttf%22,%22weight%22:%22400%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-RegularItalic.ttf%22,%22weight%22:%22400%22,%22style%22:%22italic%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-Medium.ttf%22,%22weight%22:%22500%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-MediumItalic.ttf%22,%22weight%22:%22500%22,%22style%22:%22italic%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-SemiBold.ttf%22,%22weight%22:%22600%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-SemiBoldItalic.ttf%22,%22weight%22:%22600%22,%22style%22:%22italic%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-Bold.ttf%22,%22weight%22:%22700%22,%22style%22:%22normal%22},{%22path%22:%22../../../public/fonts/metro-sans/MetroSans-BoldItalic.ttf%22,%22weight%22:%22700%22,%22style%22:%22italic%22}],%22variable%22:%22--font-metro-sans%22,%22display%22:%22swap%22}],%22variableName%22:%22metroSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'metroSans', 'metroSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts"], "sourcesContent": ["import {getRequestConfig} from 'next-intl/server';\n\n// Can be imported from a shared config\nconst locales = ['bs', 'en', 'de'] as const;\n\nexport default getRequestConfig(async ({requestLocale}) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  if (!locale || !locales.includes(locale as typeof locales[number])) {\n    locale = 'bs';\n  }\n\n  return {\n    locale,\n    messages: (await import(`../../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;uCAEnB,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAC,aAAa,EAAC;IACpD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,SAAmC;QAClE,SAAS;IACX;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport localFont from \"next/font/local\";\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport \"./globals.css\";\n\nconst metroSans = localFont({\n  src: [\n    // Light weights\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-Light.ttf',\n      weight: '300',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-LightItalic.ttf',\n      weight: '300',\n      style: 'italic',\n    },\n    // Book weights (normal)\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-Book.ttf',\n      weight: '400',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-BookItalic.ttf',\n      weight: '400',\n      style: 'italic',\n    },\n    // Regular weights (also normal, but different variant)\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-Regular.ttf',\n      weight: '400',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-RegularItalic.ttf',\n      weight: '400',\n      style: 'italic',\n    },\n    // Medium weights\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-Medium.ttf',\n      weight: '500',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-MediumItalic.ttf',\n      weight: '500',\n      style: 'italic',\n    },\n    // SemiBold weights\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-SemiBold.ttf',\n      weight: '600',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-SemiBoldItalic.ttf',\n      weight: '600',\n      style: 'italic',\n    },\n    // Bold weights\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-Bold.ttf',\n      weight: '700',\n      style: 'normal',\n    },\n    {\n      path: '../../../public/fonts/metro-sans/MetroSans-BoldItalic.ttf',\n      weight: '700',\n      style: 'italic',\n    },\n  ],\n  variable: '--font-metro-sans',\n  display: 'swap',\n});\n\nexport const metadata: Metadata = {\n  title: \"AlbatrosDoc\",\n  description: \"Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe\",\n};\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: {\n  children: React.ReactNode;\n  params: Promise<{ locale: string }>;\n}) {\n  const { locale } = await params;\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale}>\n      <body\n        className={`${metroSans.variable} antialiased font-metro-sans`}\n      >\n        <NextIntlClientProvider messages={messages}>\n          {children}\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;;;;AA4EO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;YACC,WAAW,GAAG,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,4BAA4B,CAAC;sBAE9D,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAC/B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}