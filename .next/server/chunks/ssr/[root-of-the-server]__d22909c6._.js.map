{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/ui/NavButton.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface NavButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'language' | 'mobile' | 'dropdown-item';\n  isScrolled?: boolean;\n  isActive?: boolean;\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst NavButton = React.forwardRef<HTMLButtonElement, NavButtonProps>(\n  ({ \n    variant = 'language', \n    isScrolled = false,\n    isActive = false,\n    children, \n    className, \n    ...props \n  }, ref) => {\n    const baseStyles = `\n      transition-all duration-300 focus:outline-none\n    `;\n\n    const variants = {\n      language: `\n        flex items-center space-x-3 px-4 py-2 text-sm font-medium rounded-xl border\n        ${isScrolled \n          ? 'text-white/80 hover:text-white hover:bg-white/10 border-white/20' \n          : 'text-white/90 hover:text-white hover:bg-white/10 border-white/30 backdrop-blur-sm drop-shadow-lg'\n        }\n      `,\n      mobile: `\n        p-2 rounded-lg\n        ${isScrolled\n          ? 'text-white/80 hover:text-white hover:bg-white/10'\n          : 'text-white/90 hover:text-white hover:bg-white/10 backdrop-blur-sm drop-shadow-lg'\n        }\n      `,\n      'dropdown-item': `\n        flex items-center space-x-3 w-full px-4 py-3 text-sm \n        hover:bg-white/10 transition-colors rounded-lg mx-2\n        ${isActive \n          ? 'bg-blue-500/20 text-blue-300' \n          : 'text-white/80 hover:text-white'\n        }\n      `\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseStyles,\n          variants[variant],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n);\n\nNavButton.displayName = 'NavButton';\n\nexport default NavButton;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EACC,UAAU,UAAU,EACpB,aAAa,KAAK,EAClB,WAAW,KAAK,EAChB,QAAQ,EACR,SAAS,EACT,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;IAEpB,CAAC;IAED,MAAM,WAAW;QACf,UAAU,CAAC;;QAET,EAAE,aACE,qEACA,mGACH;MACH,CAAC;QACD,QAAQ,CAAC;;QAEP,EAAE,aACE,qDACA,mFACH;MACH,CAAC;QACD,iBAAiB,CAAC;;;QAGhB,EAAE,WACE,iCACA,iCACH;MACH,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations, useLocale } from 'next-intl';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport NavButton from '@/components/ui/NavButton';\n\nconst Navigation = () => {\n  const t = useTranslations('nav');\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLangOpen, setIsLangOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = window.scrollY;\n      setIsScrolled(scrollTop > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const languages = [\n    { code: 'bs', name: '<PERSON><PERSON><PERSON>', flag: '🇧🇦' },\n    { code: 'en', name: 'English', flag: '🇺🇸' },\n    { code: 'de', name: 'Deutsch', flag: '🇩🇪' }\n  ];\n\n  const currentLanguage = languages.find(lang => lang.code === locale);\n\n  const switchLanguage = (newLocale: string) => {\n    const segments = pathname.split('/');\n\n    if (newLocale === 'bs') {\n      // For Bosnian (default), remove the locale from the path\n      if (segments[1] === 'en' || segments[1] === 'de') {\n        // Remove the current locale segment\n        segments.splice(1, 1);\n        const newPath = segments.join('/') || '/';\n        router.push(newPath);\n      }\n      // If already on Bosnian (no locale in path), no navigation needed\n    } else {\n      // For non-default locales (en, de), add/replace the locale in the path\n      if (segments[1] === 'en' || segments[1] === 'de') {\n        // Replace existing locale\n        segments[1] = newLocale;\n      } else {\n        // Add locale to path (current path is default/bs)\n        segments.splice(1, 0, newLocale);\n      }\n      const newPath = segments.join('/');\n      router.push(newPath);\n    }\n\n    setIsLangOpen(false);\n  };\n\n  return (\n    <nav className={`fixed w-full top-0 z-50 transition-all duration-500 animate-fade-in ${\n      isScrolled\n        ? 'bg-black'\n        : 'bg-transparent'\n    }`}>\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <div className={`flex justify-between items-center h-24`}>\n          {/* Logo */}\n          <div className=\"flex-shrink-0 flex items-center\">\n            <div className=\"w-12 h-12 relative\">\n              <Image\n                src=\"/logo.svg\"\n                alt=\"AlbatrosDoc Logo\"\n                width={48}\n                height={48}\n                className=\"w-full h-full object-contain\"\n                style={{\n                  filter: 'brightness(0) saturate(100%) invert(98%) sepia(8%) saturate(346%) hue-rotate(75deg) brightness(106%) contrast(96%)'\n                }}\n              />\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"flex items-center space-x-12\">\n              <a href=\"#\" className={`font-light text-lg transition-all duration-300 relative group ${\n                isScrolled ? 'text-albatros-ivory hover:text-albatros-ivory' : 'text-albatros-ivory hover:text-albatros-ivory drop-shadow-lg'\n              }`}>\n                {t('home')}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300\"></span>\n              </a>\n              <a href=\"#\" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${\n                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'\n              }`}>\n                {t('about')}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300\"></span>\n              </a>\n              <a href=\"#\" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${\n                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'\n              }`}>\n                {t('services')}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300\"></span>\n              </a>\n              <a href=\"#\" className={`font-light text-lg transition-all duration-300 relative group text-albatros-ivory/90 ${\n                isScrolled ? 'hover:text-albatros-ivory' : 'hover:text-albatros-ivory drop-shadow-lg'\n              }`}>\n                {t('contact')}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-white group-hover:w-full transition-all duration-300\"></span>\n              </a>\n            </div>\n          </div>\n\n          {/* Language Switcher & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Language Switcher */}\n            <div className=\"relative\">\n              <NavButton\n                variant=\"language\"\n                isScrolled={isScrolled}\n                onClick={() => setIsLangOpen(!isLangOpen)}\n              >\n                <span className=\"text-lg\">{currentLanguage?.flag}</span>\n                <span className=\"hidden sm:block font-medium\">{currentLanguage?.name}</span>\n                <svg className=\"w-4 h-4 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </NavButton>\n\n              {isLangOpen && (\n                <div className=\"absolute right-0 mt-2 w-48 bg-slate-800/95 backdrop-blur-xl rounded-xl shadow-2xl py-2 z-50 border border-white/10\">\n                  {languages.map((lang) => (\n                    <NavButton\n                      key={lang.code}\n                      variant=\"dropdown-item\"\n                      isActive={locale === lang.code}\n                      onClick={() => switchLanguage(lang.code)}\n                    >\n                      <span>{lang.flag}</span>\n                      <span>{lang.name}</span>\n                    </NavButton>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <NavButton\n                variant=\"mobile\"\n                isScrolled={isScrolled}\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n              >\n                <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  {isMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </NavButton>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className={`px-4 pt-4 pb-6 space-y-2 backdrop-blur-xl transition-all duration-300 ${\n              isScrolled\n                ? 'bg-slate-800/95'\n                : 'bg-slate-900/90'\n            }`}>\n              <a href=\"#\" className=\"text-white hover:text-blue-300 block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300\">\n                {t('home')}\n              </a>\n              <a href=\"#\" className=\"text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300\">\n                {t('about')}\n              </a>\n              <a href=\"#\" className=\"text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300\">\n                {t('services')}\n              </a>\n              <a href=\"#\" className=\"text-white/80 hover:text-white block px-4 py-3 text-base font-medium rounded-lg hover:bg-white/10 transition-all duration-300\">\n                {t('contact')}\n              </a>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,YAAY,OAAO,OAAO;YAChC,cAAc,YAAY;QAC5B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAM,MAAM;YAAY,MAAM;QAAO;QAC7C;YAAE,MAAM;YAAM,MAAM;YAAW,MAAM;QAAO;QAC5C;YAAE,MAAM;YAAM,MAAM;YAAW,MAAM;QAAO;KAC7C;IAED,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAE7D,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,SAAS,KAAK,CAAC;QAEhC,IAAI,cAAc,MAAM;YACtB,yDAAyD;YACzD,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM;gBAChD,oCAAoC;gBACpC,SAAS,MAAM,CAAC,GAAG;gBACnB,MAAM,UAAU,SAAS,IAAI,CAAC,QAAQ;gBACtC,OAAO,IAAI,CAAC;YACd;QACA,kEAAkE;QACpE,OAAO;YACL,uEAAuE;YACvE,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE,KAAK,MAAM;gBAChD,0BAA0B;gBAC1B,QAAQ,CAAC,EAAE,GAAG;YAChB,OAAO;gBACL,kDAAkD;gBAClD,SAAS,MAAM,CAAC,GAAG,GAAG;YACxB;YACA,MAAM,UAAU,SAAS,IAAI,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd;QAEA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,oEAAoE,EACnF,aACI,aACA,kBACJ;kBACA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,sCAAsC,CAAC;;sCAEtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,OAAO;wCACL,QAAQ;oCACV;;;;;;;;;;;;;;;;sCAMN,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAW,CAAC,8DAA8D,EACpF,aAAa,kDAAkD,gEAC/D;;4CACC,EAAE;0DACH,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAE,MAAK;wCAAI,WAAW,CAAC,qFAAqF,EAC3G,aAAa,8BAA8B,4CAC3C;;4CACC,EAAE;0DACH,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAE,MAAK;wCAAI,WAAW,CAAC,qFAAqF,EAC3G,aAAa,8BAA8B,4CAC3C;;4CACC,EAAE;0DACH,8OAAC;gDAAK,WAAU;;;;;;;;;;;;kDAElB,8OAAC;wCAAE,MAAK;wCAAI,WAAW,CAAC,qFAAqF,EAC3G,aAAa,8BAA8B,4CAC3C;;4CACC,EAAE;0DACH,8OAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qIAAA,CAAA,UAAS;4CACR,SAAQ;4CACR,YAAY;4CACZ,SAAS,IAAM,cAAc,CAAC;;8DAE9B,8OAAC;oDAAK,WAAU;8DAAW,iBAAiB;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAA+B,iBAAiB;;;;;;8DAChE,8OAAC;oDAAI,WAAU;oDAA4C,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACnG,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,4BACC,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,qIAAA,CAAA,UAAS;oDAER,SAAQ;oDACR,UAAU,WAAW,KAAK,IAAI;oDAC9B,SAAS,IAAM,eAAe,KAAK,IAAI;;sEAEvC,8OAAC;sEAAM,KAAK,IAAI;;;;;;sEAChB,8OAAC;sEAAM,KAAK,IAAI;;;;;;;mDANX,KAAK,IAAI;;;;;;;;;;;;;;;;8CAcxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,qIAAA,CAAA,UAAS;wCACR,SAAQ;wCACR,YAAY;wCACZ,SAAS,IAAM,cAAc,CAAC;kDAE9B,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC7D,2BACC,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;yGAErE,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShF,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,CAAC,sEAAsE,EACrF,aACI,oBACA,mBACJ;;0CACA,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACnB,EAAE;;;;;;0CAEL,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACnB,EAAE;;;;;;0CAEL,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACnB,EAAE;;;;;;0CAEL,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACnB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'white' | 'accent' | 'ghost' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n  className?: string;\n  isLoading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    variant = 'primary', \n    size = 'md', \n    children, \n    className, \n    isLoading = false,\n    disabled,\n    ...props \n  }, ref) => {\n    const baseStyles = `\n      inline-flex items-center justify-center font-medium\n      transition-all duration-300 ease-in-out\n      focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      cursor-pointer rounded-full\n      [background-size:100%_100%]\n    `;\n\n    const variants = {\n      primary: `\n        bg-gradient-to-r from-albatros-black to-albatros-indigo-dye\n        hover:[background-size:140%_150%]\n        text-albatros-ivory shadow-md\n        hover:shadow-2xl\n        focus-visible:ring-albatros-moonstone\n      `,\n      secondary: `\n        bg-gradient-to-r from-albatros-moonstone to-albatros-ivory\n        hover:[background-size:130%_120%]\n        text-albatros-black shadow-md\n        hover:shadow-xl\n        focus-visible:ring-albatros-moonstone\n      `,\n      white: `\n        bg-white\n        border-2 border-white\n        hover:bg-transparent\n        text-black shadow-md\n        hover:text-white\n        hover:shadow-2xl\n        focus-visible:ring-albatros-moonstone\n      `,\n      accent: `\n        bg-gradient-to-r from-albatros-carmine to-albatros-carmine/60\n        hover:[background-size:150%_130%]\n        text-albatros-ivory shadow-md\n        hover:shadow-xl\n        focus-visible:ring-albatros-carmine\n      `,\n      ghost: `\n        bg-gradient-to-r from-transparent to-albatros-indigo-dye/5\n        hover:[background-size:120%_110%]\n        text-albatros-indigo-dye\n        hover:text-albatros-black hover:shadow-md\n        focus-visible:ring-albatros-indigo-dye\n      `,\n      outline: `\n        border-2 border-albatros-indigo-dye text-albatros-indigo-dye\n        bg-gradient-to-r from-transparent to-transparent\n        hover:[background-size:120%_110%] hover:from-albatros-indigo-dye/10 hover:to-albatros-moonstone/20\n        hover:border-albatros-moonstone hover:text-albatros-black shadow-sm hover:shadow-md\n        focus-visible:ring-albatros-indigo-dye\n      `\n    };\n\n    const sizes = {\n      sm: 'px-6 py-2 text-sm',\n      md: 'px-10 py-5 text-lg font-normal',\n      lg: 'px-12 py-5 text-lg font-normal'\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"animate-spin h-4 w-4 mr-2\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EACC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAC;;;;;;;IAOpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;;;;MAMV,CAAC;QACD,WAAW,CAAC;;;;;;MAMZ,CAAC;QACD,OAAO,CAAC;;;;;;;;MAQR,CAAC;QACD,QAAQ,CAAC;;;;;;MAMT,CAAC;QACD,OAAO,CAAC;;;;;;MAMR,CAAC;QACD,SAAS,CAAC;;;;;;MAMV,CAAC;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport { useInView } from 'react-intersection-observer';\nimport Button from '@/components/ui/Button';\n\nconst HeroSection = () => {\n  const t = useTranslations('hero');\n\n  // Intersection observers for staggered animations\n  const { ref: titleRef, inView: titleInView } = useInView({\n    threshold: 0.3,\n    triggerOnce: true,\n  });\n\n  const { ref: descriptionRef, inView: descriptionInView } = useInView({\n    threshold: 0.3,\n    triggerOnce: true,\n  });\n\n  const { ref: buttonRef, inView: buttonInView } = useInView({\n    threshold: 0.3,\n    triggerOnce: true,\n  });\n\n  return (\n    <section className=\"relative h-screen py-20 flex items-end justify-center overflow-hidden\">\n      {/* Hero Background Image */}\n      <div\n        className=\"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat animate-fade-in-hero-bg brightness-75\"\n        style={{\n          backgroundImage: 'url(/images/hero.png)'\n        }}\n      ></div>\n\n      <div className=\"relative w-full mt-10 max-w-7xl mx-auto px-6 lg:px-8 z-20\">\n        <div className=\"space-y-8\">\n          {/* Main Title - Bold and Impactful */}\n          <div className=\"space-y-8\">\n            <h1\n              ref={titleRef}\n              className={`text-5xl lg:text-6xl xl:text-7xl font-bold block max-w-4xl text-albatros-ivory mb-4 transition-all duration-1000 ease-out delay-700 ${\n                titleInView\n                  ? 'opacity-100 translate-y-0'\n                  : 'opacity-0 translate-y-8'\n              }`}\n            >\n              {t('title')}\n            </h1>\n\n            <div\n              ref={descriptionRef}\n              className={`max-w-3xl transition-all duration-1000 ease-out delay-1000 ${\n                descriptionInView\n                  ? 'opacity-100 translate-y-0'\n                  : 'opacity-0 translate-y-8'\n              }`}\n            >\n              <p className=\"text-xl lg:text-2xl text-albatros-ivory/90 font-light leading-relaxed\">\n                {t('description')}\n              </p>\n            </div>\n          </div>\n\n          {/* Enhanced CTA with modern styling */}\n          <div\n            ref={buttonRef}\n            className={`flex flex-col sm:flex-row gap-6 transition-all duration-1000 ease-out delay-1300 ${\n              buttonInView\n                ? 'opacity-100 translate-y-0'\n                : 'opacity-0 translate-y-8'\n            }`}\n          >\n            <Button variant=\"white\" size=\"md\">\n              {t('contactButton')}\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,cAAc;IAClB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,kDAAkD;IAClD,MAAM,EAAE,KAAK,QAAQ,EAAE,QAAQ,WAAW,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QACvD,WAAW;QACX,aAAa;IACf;IAEA,MAAM,EAAE,KAAK,cAAc,EAAE,QAAQ,iBAAiB,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QACnE,WAAW;QACX,aAAa;IACf;IAEA,MAAM,EAAE,KAAK,SAAS,EAAE,QAAQ,YAAY,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QACzD,WAAW;QACX,aAAa;IACf;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK;oCACL,WAAW,CAAC,oIAAoI,EAC9I,cACI,8BACA,2BACJ;8CAED,EAAE;;;;;;8CAGL,8OAAC;oCACC,KAAK;oCACL,WAAW,CAAC,2DAA2D,EACrE,oBACI,8BACA,2BACJ;8CAEF,cAAA,8OAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BACC,KAAK;4BACL,WAAW,CAAC,iFAAiF,EAC3F,eACI,8BACA,2BACJ;sCAEF,cAAA,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC1B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;uCAEe", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\n\nconst AboutSection = () => {\n  const t = useTranslations('hero');\n  const features = useTranslations('features');\n\n  return (\n    <section className=\"min-h-screen flex\">\n      {/* Left Half - Image */}\n      <div className=\"w-1/2 relative overflow-hidden\">\n        <div\n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: 'url(/images/building.png)'\n          }}\n        ></div>\n        {/* Optional overlay for better text contrast if needed */}\n        <div className=\"absolute inset-0 bg-black/10\"></div>\n      </div>\n\n      {/* Right Half - Content */}\n      <div className=\"w-1/2 bg-albatros-ivory flex items-center justify-center px-12 lg:px-16\">\n        <div className=\"max-w-2xl space-y-12\">\n          {/* Main Title */}\n          <div className=\"space-y-8\">\n            <h2 className=\"text-4xl lg:text-5xl xl:text-6xl font-bold text-albatros-black leading-tight\">\n              Uz Albatros dokumenti klijenata stižu brzo i sigurno, bez čekanja i nepotrebnih briga.\n            </h2>\n\n            {/* Divider Line */}\n            <div className=\"w-24 h-1 bg-albatros-carmine\"></div>\n          </div>\n\n          {/* Features List */}\n          <div className=\"space-y-8\">\n            <div className=\"flex items-center space-x-4 text-albatros-black\">\n              <div className=\"w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0\"></div>\n              <span className=\"text-xl lg:text-2xl font-medium\">{features('fast')}</span>\n            </div>\n\n            <div className=\"flex items-center space-x-4 text-albatros-black\">\n              <div className=\"w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0\"></div>\n              <span className=\"text-xl lg:text-2xl font-medium\">{features('reliable')}</span>\n            </div>\n\n            <div className=\"flex items-center space-x-4 text-albatros-black\">\n              <div className=\"w-2 h-2 bg-albatros-carmine rounded-full flex-shrink-0\"></div>\n              <span className=\"text-xl lg:text-2xl font-medium\">{features('discrete')}</span>\n            </div>\n          </div>\n\n          {/* Bottom Text */}\n          <div className=\"pt-8 border-t border-albatros-black/20\">\n            <p className=\"text-lg lg:text-xl text-albatros-black/80 leading-relaxed\">\n              {t('cta')}\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,eAAe;IACnB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;wBACnB;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+E;;;;;;8CAK7F,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAmC,SAAS;;;;;;;;;;;;8CAG9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAmC,SAAS;;;;;;;;;;;;8CAG9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAmC,SAAS;;;;;;;;;;;;;;;;;;sCAKhE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;uCAEe", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport Button from '@/components/ui/Button';\n\nconst FeaturesSection = () => {\n  const t = useTranslations('features');\n\n  const features = [\n    {\n      key: 'fast',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n        </svg>\n      ),\n      gradient: 'from-yellow-400 to-orange-500'\n    },\n    {\n      key: 'reliable',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n        </svg>\n      ),\n      gradient: 'from-green-400 to-emerald-500'\n    },\n    {\n      key: 'discrete',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n        </svg>\n      ),\n      gradient: 'from-blue-400 to-indigo-500'\n    }\n  ];\n\n  return (\n    <section className=\"py-32 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        {/* Geometric elements */}\n        <div className=\"absolute top-20 left-20 w-72 h-72 border border-white/10 rounded-full\"></div>\n        <div className=\"absolute bottom-20 right-20 w-96 h-96 border border-blue-400/20 rounded-full\"></div>\n\n        {/* Floating dots */}\n        <div className=\"absolute top-1/4 right-1/4 w-3 h-3 bg-blue-400 rounded-full animate-pulse\"></div>\n        <div className=\"absolute bottom-1/3 left-1/3 w-2 h-2 bg-white/60 rounded-full animate-pulse delay-1000\"></div>\n\n        {/* Gradient overlays */}\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-transparent rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-indigo-500/20 to-transparent rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8 z-10\">\n        {/* Section Header */}\n        <div className=\"text-center mb-20\">\n          <div className=\"inline-block px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-semibold mb-6\">\n            Naše prednosti\n          </div>\n          <h2 className=\"text-5xl md:text-6xl font-black leading-tight text-white mb-6\">\n            Tehnologija i\n            <span className=\"block bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent\">\n              inovacija uticaj\n            </span>\n          </h2>\n          <p className=\"text-xl text-blue-100/80 max-w-2xl mx-auto leading-relaxed\">\n            Tri ključna elementa koji nas čine vašim idealnim partnerom za budućnost\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={feature.key}\n              className={`group relative bg-white/10 backdrop-blur-xl rounded-3xl p-10 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-4 border border-white/20 ${\n                index === 1 ? 'md:mt-12' : ''\n              }`}\n            >\n              {/* Icon */}\n              <div className={`relative w-20 h-20 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center text-white mb-8 group-hover:scale-110 transition-transform duration-300 shadow-2xl shadow-blue-500/25`}>\n                {feature.icon}\n              </div>\n\n              {/* Content */}\n              <div className=\"relative\">\n                <h3 className=\"text-2xl font-bold text-white mb-6 group-hover:text-blue-300 transition-colors duration-300\">\n                  {t(feature.key as 'fast' | 'reliable' | 'discrete')}\n                </h3>\n\n                {/* Description based on feature */}\n                <p className=\"text-blue-100/80 leading-relaxed text-lg\">\n                  {feature.key === 'fast' && 'Brza obrada i dostava dokumenata u najkraćem mogućem roku uz najnoviju tehnologiju.'}\n                  {feature.key === 'reliable' && 'Pouzdanost i profesionalnost u svakom koraku procesa sa 99.9% uspešnosti.'}\n                  {feature.key === 'discrete' && 'Potpuna diskrecija i sigurnost vaših ličnih podataka sa end-to-end enkripcijom.'}\n                </p>\n              </div>\n\n              {/* Decorative Element */}\n              <div className={`absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r ${feature.gradient} rounded-b-3xl transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500`}></div>\n\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-20\">\n          <p className=\"text-blue-200 text-lg mb-8\">\n            Spremni da iskusite razliku?\n          </p>\n          <Button variant=\"primary\" size=\"md\">\n            Započnite danas\n          </Button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,WAAW;QACf;YACE,KAAK;YACL,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;QACZ;QACA;YACE,KAAK;YACL,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;QACZ;QACA;YACE,KAAK;YACL,oBACE,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8F;;;;;;0CAG7G,8OAAC;gCAAG,WAAU;;oCAAgE;kDAE5E,8OAAC;wCAAK,WAAU;kDAAiF;;;;;;;;;;;;0CAInG,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAEC,WAAW,CAAC,iKAAiK,EAC3K,UAAU,IAAI,aAAa,IAC3B;;kDAGF,8OAAC;wCAAI,WAAW,CAAC,qCAAqC,EAAE,QAAQ,QAAQ,CAAC,mJAAmJ,CAAC;kDAC1N,QAAQ,IAAI;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,EAAE,QAAQ,GAAG;;;;;;0DAIhB,8OAAC;gDAAE,WAAU;;oDACV,QAAQ,GAAG,KAAK,UAAU;oDAC1B,QAAQ,GAAG,KAAK,cAAc;oDAC9B,QAAQ,GAAG,KAAK,cAAc;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAW,CAAC,qDAAqD,EAAE,QAAQ,QAAQ,CAAC,4FAA4F,CAAC;;;;;;kDAGtL,8OAAC;wCAAI,WAAU;;;;;;;+BA5BV,QAAQ,GAAG;;;;;;;;;;kCAkCtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;uCAEe", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport Button from '@/components/ui/Button';\n\nconst CTASection = () => {\n  const t = useTranslations('hero');\n\n  return (\n    <section className=\"py-32 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\">\n      {/* Sophisticated Background Elements */}\n      <div className=\"absolute inset-0\">\n        {/* Large geometric shapes */}\n        <div className=\"absolute top-20 right-20 w-80 h-80 border border-gray-200 rounded-full opacity-30\"></div>\n        <div className=\"absolute bottom-20 left-20 w-64 h-64 border border-blue-200 rounded-full opacity-40\"></div>\n\n        {/* Gradient overlays */}\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-100/50 to-transparent rounded-full blur-3xl\"></div>\n\n        {/* Connecting lines */}\n        <svg className=\"absolute inset-0 w-full h-full opacity-20\" xmlns=\"http://www.w3.org/2000/svg\">\n          <defs>\n            <linearGradient id=\"ctaLineGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"rgba(59, 130, 246, 0.3)\" />\n              <stop offset=\"100%\" stopColor=\"rgba(156, 163, 175, 0.2)\" />\n            </linearGradient>\n          </defs>\n          <path d=\"M 200 100 Q 500 200 800 150\" stroke=\"url(#ctaLineGradient)\" strokeWidth=\"1\" fill=\"none\" />\n          <path d=\"M 100 400 Q 400 300 700 450\" stroke=\"url(#ctaLineGradient)\" strokeWidth=\"1\" fill=\"none\" />\n        </svg>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8\">\n        <div className=\"text-center space-y-16\">\n          {/* Main Content */}\n          <div className=\"space-y-8\">\n            <div className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6\">\n              Spremni za početak?\n            </div>\n            <h2 className=\"text-5xl md:text-6xl font-black leading-tight text-gray-900\">\n              Bolt Lifetime data\n              <span className=\"block bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent\">\n                tracking\n              </span>\n            </h2>\n\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              {t('cta')} Pridružite se hiljadama zadovoljnih klijenata koji su već iskusili našu revolucionarnu uslugu.\n            </p>\n          </div>\n\n          {/* Enhanced CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n            <Button variant=\"primary\" size=\"lg\">\n              {t('contactButton')}\n            </Button>\n\n            <Button variant=\"secondary\" size=\"lg\" className=\"flex items-center space-x-3\">\n              <span>{t('learnMore')}</span>\n              <svg className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </Button>\n          </div>\n\n          {/* Enhanced Trust Indicators */}\n          <div className=\"pt-16\">\n            <p className=\"text-gray-500 mb-12 text-lg\">Zašto nas biraju klijenti širom sveta</p>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105\">\n                <div className=\"flex flex-col items-center text-center space-y-4\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-bold text-gray-900 text-xl\">100% Pouzdano</p>\n                    <p className=\"text-gray-600\">Garantovana usluga sa 99.9% uspešnosti</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105\">\n                <div className=\"flex flex-col items-center text-center space-y-4\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-bold text-gray-900 text-xl\">24h Dostava</p>\n                    <p className=\"text-gray-600\">Brza obrada sa AI tehnologijom</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105\">\n                <div className=\"flex flex-col items-center text-center space-y-4\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg\">\n                    <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-bold text-gray-900 text-xl\">Potpuna Diskrecija</p>\n                    <p className=\"text-gray-600\">End-to-end enkripcija podataka</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Stats Section */}\n          <div className=\"pt-16 border-t border-gray-200\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-black text-blue-600 mb-2\">10K+</div>\n                <div className=\"text-gray-600\">Zadovoljnih klijenata</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-black text-green-600 mb-2\">99.9%</div>\n                <div className=\"text-gray-600\">Uspešnost</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-black text-purple-600 mb-2\">24h</div>\n                <div className=\"text-gray-600\">Prosečno vreme</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-black text-cyan-600 mb-2\">5★</div>\n                <div className=\"text-gray-600\">Prosečna ocena</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CTASection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;wBAA4C,OAAM;;0CAC/D,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAkB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAChE,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;0CAGlC,8OAAC;gCAAK,GAAE;gCAA8B,QAAO;gCAAwB,aAAY;gCAAI,MAAK;;;;;;0CAC1F,8OAAC;gCAAK,GAAE;gCAA8B,QAAO;gCAAwB,aAAY;gCAAI,MAAK;;;;;;;;;;;;;;;;;;0BAI9F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2F;;;;;;8CAG1G,8OAAC;oCAAG,WAAU;;wCAA8D;sDAE1E,8OAAC;4CAAK,WAAU;sDAAiF;;;;;;;;;;;;8CAKnG,8OAAC;oCAAE,WAAU;;wCACV,EAAE;wCAAO;;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAC5B,EAAE;;;;;;8CAGL,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAK,WAAU;;sDAC9C,8OAAC;sDAAM,EAAE;;;;;;sDACT,8OAAC;4CAAI,WAAU;4CAAsE,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC7H,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAkC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAkC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC5E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAkC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;uCAEe", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport { useEffect } from 'react';\nimport gradientGL from 'gradient-gl';\n\nconst Footer = () => {\n  const t = useTranslations('nav');\n\n  useEffect(() => {\n    // Initialize gradient-gl with the specified seed and target the gradient container\n    gradientGL('f2.fc78', '#footer-gradient-bg');\n  }, []);\n\n  return (\n    <footer className=\"text-white py-24 relative overflow-hidden\">\n      {/* WebGL Gradient Background */}\n      <div id=\"footer-gradient-bg\" className=\"gradient-container absolute inset-0 z-0\"></div>\n      <div className=\"bg-albatros-indigo-dye absolute left-0 top-0 w-full h-full opacity-70\"></div>\n\n      <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8 z-20\">\n        <div className=\"grid md:grid-cols-4 gap-12\">\n          {/* Logo and Description */}\n          <div className=\"md:col-span-2\">\n            <div className=\"flex items-center mb-8\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-2xl flex items-center justify-center shadow-2xl shadow-blue-500/25\">\n                <span className=\"text-slate-900 font-bold text-2xl\">A</span>\n              </div>\n              <span className=\"ml-4 text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">AlbatrosDoc</span>\n            </div>\n            <p className=\"text-blue-100/80 leading-relaxed max-w-lg text-lg mb-8\">\n              Vaš pouzdan partner za brzo, sigurno i profesionalno pribavljanje i dostavu dokumenata.\n              Štedimo vaše vrijeme i smanjujemo stres u administrativnim poslovima.\n            </p>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"font-semibold text-xl mb-6 text-white\">Brzi linkovi</h3>\n            <ul className=\"space-y-4\">\n              <li>\n                <a href=\"#\" className=\"text-blue-200/80 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group\">\n                  <span className=\"flex items-center\">\n                    {t('home')}\n                    <svg className=\"w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </span>\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-200/80 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group\">\n                  <span className=\"flex items-center\">\n                    {t('about')}\n                    <svg className=\"w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </span>\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-200/80 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group\">\n                  <span className=\"flex items-center\">\n                    {t('services')}\n                    <svg className=\"w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </span>\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-blue-200/80 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group\">\n                  <span className=\"flex items-center\">\n                    {t('contact')}\n                    <svg className=\"w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                    </svg>\n                  </span>\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"font-semibold text-xl mb-6 text-white\">Kontakt</h3>\n            <div className=\"space-y-6 text-blue-200/80\">\n              <div className=\"group bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300\">\n                    <svg className=\"w-5 h-5 text-slate-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <span className=\"group-hover:text-white transition-colors font-medium\"><EMAIL></span>\n                </div>\n              </div>\n\n              <div className=\"group bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-300 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300\">\n                    <svg className=\"w-5 h-5 text-slate-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                    </svg>\n                  </div>\n                  <span className=\"group-hover:text-white transition-colors font-medium\">+387 XX XXX XXX</span>\n                </div>\n              </div>\n\n              <div className=\"group bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-300 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300\">\n                    <svg className=\"w-5 h-5 text-slate-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    </svg>\n                  </div>\n                  <span className=\"group-hover:text-white transition-colors font-medium\">Sarajevo, BiH</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-white/20 mt-16 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-blue-200/80 text-lg\">\n              © 2024 AlbatrosDoc. Sva prava zadržana.\n            </p>\n            <div className=\"flex space-x-6 mt-6 md:mt-0\">\n              <a href=\"#\" className=\"group w-12 h-12 bg-white/10 backdrop-blur-xl rounded-xl flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-110\">\n                <span className=\"sr-only\">Facebook</span>\n                <svg className=\"w-6 h-6 text-blue-200 group-hover:text-white transition-colors\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"group w-12 h-12 bg-white/10 backdrop-blur-xl rounded-xl flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-110\">\n                <span className=\"sr-only\">LinkedIn</span>\n                <svg className=\"w-6 h-6 text-blue-200 group-hover:text-white transition-colors\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"group w-12 h-12 bg-white/10 backdrop-blur-xl rounded-xl flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-110\">\n                <span className=\"sr-only\">Twitter</span>\n                <svg className=\"w-6 h-6 text-blue-200 group-hover:text-white transition-colors\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"group w-12 h-12 bg-white/10 backdrop-blur-xl rounded-xl flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-110\">\n                <span className=\"sr-only\">Instagram</span>\n                <svg className=\"w-6 h-6 text-blue-200 group-hover:text-white transition-colors\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Additional Links */}\n          <div className=\"flex flex-wrap justify-center md:justify-start gap-6 mt-8 pt-6 border-t border-white/10\">\n            <a href=\"#\" className=\"text-blue-200/60 hover:text-white text-sm transition-colors duration-300\">Politika privatnosti</a>\n            <a href=\"#\" className=\"text-blue-200/60 hover:text-white text-sm transition-colors duration-300\">Uslovi korišćenja</a>\n            <a href=\"#\" className=\"text-blue-200/60 hover:text-white text-sm transition-colors duration-300\">Kolačići</a>\n            <a href=\"#\" className=\"text-blue-200/60 hover:text-white text-sm transition-colors duration-300\">Sitemap</a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mFAAmF;QACnF,CAAA,GAAA,uIAAA,CAAA,UAAU,AAAD,EAAE,WAAW;IACxB,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,IAAG;gBAAqB,WAAU;;;;;;0BACvC,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;0DAEtD,8OAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAElH,8OAAC;wCAAE,WAAU;kDAAyD;;;;;;;;;;;;0CAOxE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAK,WAAU;;4DACb,EAAE;0EACH,8OAAC;gEAAI,WAAU;gEAAiF,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACxI,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK7E,8OAAC;0DACC,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAK,WAAU;;4DACb,EAAE;0EACH,8OAAC;gEAAI,WAAU;gEAAiF,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACxI,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK7E,8OAAC;0DACC,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAK,WAAU;;4DACb,EAAE;0EACH,8OAAC;gEAAI,WAAU;gEAAiF,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACxI,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK7E,8OAAC;0DACC,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,8OAAC;wDAAK,WAAU;;4DACb,EAAE;0EACH,8OAAC;gEAAI,WAAU;gEAAiF,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACxI,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjF,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAK,WAAU;sEAAuD;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAK,WAAU;sEAAuD;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;;kFAChF,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;kFACrE,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;sEAGzE,8OAAC;4DAAK,WAAU;sEAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA2B;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;wDAAiE,MAAK;wDAAe,SAAQ;kEAC1G,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAyQ,UAAS;;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;wDAAiE,MAAK;wDAAe,SAAQ;kEAC1G,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA6b,UAAS;;;;;;;;;;;;;;;;;0DAGre,8OAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;wDAAiE,MAAK;wDAAe,SAAQ;kEAC1G,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC;wDAAI,WAAU;wDAAiE,MAAK;wDAAe,SAAQ;kEAC1G,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAmtB,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/vB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2E;;;;;;kDACjG,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2E;;;;;;kDACjG,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2E;;;;;;kDACjG,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA2E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7G;uCAEe", "debugId": null}}]}