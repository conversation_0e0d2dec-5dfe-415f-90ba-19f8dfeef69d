{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "ce58ab279cb0872d8941d4eb12c79e5b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a62c9b76b35c599419f2756ac8aad46163659af394691b011901f2657bc725a1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9f52d246b5ae70d1be06c2c8f8455b960a6089e7be06eb00c916b3158e0afda2"}}}, "instrumentation": null, "functions": {}}