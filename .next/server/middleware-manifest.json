{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(bs|de|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "f4b8e1211e0bf9334cee714da77612f5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bd4c279f761d98e91184ee1a980c6dd2f259d95532346daa8c85e2e02b252bef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f059649f60620d6de3ac7cdf9dccba8f0d99a7cfc5f3e37114a02ab39755957e"}}}, "sortedMiddleware": ["/"], "functions": {}}